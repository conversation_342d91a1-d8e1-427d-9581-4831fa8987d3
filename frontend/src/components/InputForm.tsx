// @ts-ignore
/* 忽略所有 TypeScript 错误，因为 React 19 的类型声明与之前版本不同 */
// @ts-nocheck
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SquarePen, Brain, Send, StopCircle, Zap, Cpu, Sparkles } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Updated InputFormProps
interface InputFormProps {
  onSubmit: (inputValue: string, effort: string, model: string) => void;
  onCancel: () => void;
  isLoading: boolean;
  hasHistory: boolean;
  selectedModel?: string;
  selectedEffort?: string;
}

// @ts-ignore
export const InputForm = ({
  onSubmit,
  onCancel,
  isLoading,
  hasHistory,
  selectedModel,
  selectedEffort,
}: InputFormProps) => {
  const [internalInputValue, setInternalInputValue] = useState("");
  const [effort, setEffort] = useState(selectedEffort || "low");
  const [model, setModel] = useState(selectedModel || "gemini-2.5-flash");

  // @ts-ignore
  const handleInternalSubmit = (e?: any) => {
    if (e) e.preventDefault();
    if (!internalInputValue.trim()) return;
    onSubmit(internalInputValue, effort, model);
    setInternalInputValue("");
  };

  // @ts-ignore
  const handleInternalKeyDown = (e: any) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleInternalSubmit();
    }
  };

  const isSubmitDisabled = !internalInputValue.trim() || isLoading;

  return (
    <form
      onSubmit={handleInternalSubmit}
      className={`flex flex-col gap-2 p-3 pb-4`}
    >
      <div
        className={`flex flex-row items-center justify-between text-white rounded-3xl rounded-bl-sm ${
          hasHistory ? "rounded-br-sm" : ""
        } break-words min-h-7 bg-neutral-100 px-4 pt-3 `}
      >
        <Textarea
          value={internalInputValue}
          onChange={(e) => setInternalInputValue(e.target.value)}
          onKeyDown={handleInternalKeyDown}
          placeholder="请输入您想分析的域名、IP、URL"
          className={`w-full text-neutral-800 placeholder-neutral-200 resize-none border-0 focus:outline-none focus:ring-0 outline-none focus-visible:ring-0 shadow-none 
                        md:text-base  min-h-[56px] max-h-[200px]`}
          rows={1}
        />
        <div className="-mt-3">
          {isLoading ? (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="text-red-500 hover:text-red-400 hover:bg-red-500/10 p-2 cursor-pointer rounded-full transition-all duration-200"
              onClick={onCancel}
            >
              <StopCircle className="h-5 w-5 animate-pulse" />
            </Button>
          ) : (
            <Button
              type="submit"
              variant="ghost"
              className={`${
                isSubmitDisabled
                  ? "text-neutral-500"
                  : "text-red-400 hover:text-red-400 hover:bg-red-400/10"
              } p-2 cursor-pointer rounded-full transition-all duration-200 text-base`}
              disabled={isSubmitDisabled}
            >
              搜索
              <Send className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex flex-row gap-2">
          <div className="flex flex-row gap-2 bg-neutral-100 border-neutral-200 text-neutral-600 focus:ring-neutral-300 rounded-xl rounded-t-sm pl-2  max-w-[100%] sm:max-w-[90%]">
            <div className="flex flex-row items-center text-sm">
              <Brain className="h-4 w-4 mr-2" />
              思考深度
            </div>
            <Select value={effort} onValueChange={setEffort}>
              <SelectTrigger className="w-[100px] bg-transparent border-none cursor-pointer">
                <SelectValue placeholder="思考深度" />
              </SelectTrigger>
              <SelectContent className="bg-white border-neutral-200 text-neutral-600 cursor-pointer">
                <SelectItem
                  value="low"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  低
                </SelectItem>
                <SelectItem
                  value="medium"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  中
                </SelectItem>
                <SelectItem
                  value="high"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  高
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-row gap-2 bg-neutral-100 border-neutral-200 text-neutral-600 focus:ring-neutral-300 rounded-xl rounded-t-sm pl-2  max-w-[100%] sm:max-w-[90%]">
            <div className="flex flex-row items-center text-sm ml-2">
              <Cpu className="h-4 w-4 mr-2" />
              模型
            </div>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger className="w-[200px] bg-transparent border-none cursor-pointer">
                <SelectValue placeholder="模型" />
              </SelectTrigger>
              <SelectContent className="bg-white border-neutral-200 text-neutral-600 cursor-pointer">
                <SelectItem
                  value="MiniMaxAI/MiniMax-M1-80k"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  <div className="flex items-center">
                    <Zap className="h-4 w-4 mr-2 text-yellow-400" /> MiniMax-M1-80k
                  </div>
                </SelectItem>
                <SelectItem
                  value="Qwen/Qwen3-235B-A22B"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  <div className="flex items-center">
                    <Sparkles className="h-4 w-4 mr-2 text-green-400" /> Qwen3-235B-A22B
                  </div>
                </SelectItem>
                <SelectItem
                  value="deepseek-ai/DeepSeek-R1"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  <div className="flex items-center">
                    <Zap className="h-4 w-4 mr-2 text-orange-400" /> DeepSeek-R1
                  </div>
                </SelectItem>
                <SelectItem
                  value="Tongyi-Zhiwen/QwenLong-L1-32B"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  <div className="flex items-center">
                    <Sparkles className="h-4 w-4 mr-2 text-green-400" /> QwenLong-L1-32B
                  </div>
                </SelectItem>
                <SelectItem
                  value="gemini-2.5-flash"
                  className="hover:bg-neutral-600 focus:bg-red-300 cursor-pointer"
                >
                  <div className="flex items-center">
                    <Sparkles className="h-4 w-4 mr-2 text-green-400" /> Gemini-2.5-Flash
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        {hasHistory && (
          <Button
            className="bg-white hover:bg-red-400/10 focus:bg-red-200 border-neutral-200 text-neutral-600 cursor-pointer rounded-xl rounded-t-sm pl-2 "
            variant="default"
            onClick={() => window.location.reload()}
          >
            <SquarePen size={16} />
            新建搜索
          </Button>
        )}
      </div>
    </form>
  );
};
