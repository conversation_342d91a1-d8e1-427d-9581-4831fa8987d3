// @ts-nocheck
import { InputForm } from "./InputForm";

interface WelcomeScreenProps {
  handleSubmit: (
    submittedInputValue: string,
    effort: string,
    model: string
  ) => void;
  onCancel: () => void;
  isLoading: boolean;
  selectedModel: string;
  selectedEffort: string;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  handleSubmit,
  onCancel,
  isLoading,
  selectedModel,
  selectedEffort,
}) => (
  <div className="flex flex-col items-center justify-center text-center px-4 flex-1 w-full max-w-3xl mx-auto gap-4">
    <div>
      <h1 className="text-5xl md:text-6xl font-semibold text-neutral-800 mb-3">
        威胁情报分析智能体
      </h1>
      <p className="text-xl md:text-2xl text-red-600">
        想让我帮您分析哪个域名或IP或URL?
      </p>
    </div>
    <div className="w-full mt-4">
      <InputForm
        onSubmit={handleSubmit}
        isLoading={isLoading}
        onCancel={onCancel}
        hasHistory={false}
        selectedModel={selectedModel}
        selectedEffort={selectedEffort}
      />
    </div>
    {/* <p className="text-xs text-neutral-400">
      Powered by Knownsec NDR Team.
    </p> */}
  </div>
);
