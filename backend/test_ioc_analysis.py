import requests
import json
import sys

if len(sys.argv) > 1:
    ioc = sys.argv[1]
else:
    print("请输入IOC参数")
    exit(-1)

print(f'调用API查询IOC: {ioc}')
# 调用 API 查询
api_url = f'http://192.168.110.90:40381/ZIwTuwmXcBWjYhyCdTlo/search/?searchCon={ioc}'

response = requests.get(api_url)
#response.raise_for_status()
result = response.json()
print(f'调用API查询IOC: {ioc}')
print(f'调用 API 返回结果: {json.dumps(result, indent=2, ensure_ascii=False)}')