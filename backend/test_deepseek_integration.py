#!/usr/bin/env python3
"""
Test script to verify DeepSeek integration is working correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_environment_variables():
    """Test that required environment variables are set."""
    load_dotenv()
    
    deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
    if not deepseek_api_key:
        print("❌ DEEPSEEK_API_KEY is not set")
        return False
    else:
        print("✅ DEEPSEEK_API_KEY is set")
        return True

def test_imports():
    """Test that all required imports work."""
    try:
        from agent.configuration import Configuration
        print("✅ Configuration import successful")
        
        from agent.graph import graph
        print("✅ Graph import successful")
        
        from langchain_openai import ChatOpenAI
        print("✅ ChatOpenAI import successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_configuration():
    """Test that configuration uses DeepSeek models."""
    try:
        from agent.configuration import Configuration
        config = Configuration()
        
        print(f"Query generator model: {config.query_generator_model}")
        print(f"Reflection model: {config.reflection_model}")
        print(f"Answer model: {config.answer_model}")
        
        # Check if models are set to deepseek
        if "deepseek" in config.query_generator_model.lower():
            print("✅ Query generator model configured for DeepSeek")
        else:
            print("❌ Query generator model not configured for DeepSeek")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_llm_initialization():
    """Test that LLM can be initialized with DeepSeek API."""
    try:
        from langchain_openai import ChatOpenAI

        # Test LLM initialization (without making actual API calls)
        llm = ChatOpenAI(
            model="deepseek-ai/DeepSeek-R1",
            temperature=0.7,
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.siliconflow.cn/v1",
        )
        print("✅ LLM initialization successful")
        return True
    except Exception as e:
        print(f"❌ LLM initialization error: {e}")
        return False

def test_json_parsing():
    """Test JSON parsing functionality for structured outputs."""
    try:
        import json

        # Test JSON parsing
        test_json = '{"query": ["test1", "test2"], "rationale": "test rationale"}'
        result = json.loads(test_json)

        if "query" in result and "rationale" in result:
            print("✅ JSON parsing functionality working")
            return True
        else:
            print("❌ JSON parsing failed - missing keys")
            return False
    except Exception as e:
        print(f"❌ JSON parsing error: {e}")
        return False

def test_siliconflow_chat():
    """Test chat functionality with SiliconFlow API."""
    try:
        from langchain_openai import ChatOpenAI
        import json

        deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
        if not deepseek_api_key:
            print("❌ DEEPSEEK_API_KEY not set, skipping chat test")
            return False

        print("🔄 Testing SiliconFlow chat functionality...")

        # Initialize LLM with SiliconFlow API
        llm = ChatOpenAI(
            model="deepseek-ai/DeepSeek-R1",
            temperature=0.7,
            max_retries=2,
            api_key=deepseek_api_key,
            base_url="https://api.siliconflow.cn/v1",
        )

        # Test multiple chat scenarios
        test_scenarios = [
            {
                "name": "Simple Greeting",
                "message": "Hello! How are you today?",
                "expected_keywords": ["hello", "hi", "good", "fine", "well"]
            },
            {
                "name": "Knowledge Question",
                "message": "What is the capital of France?",
                "expected_keywords": ["paris", "france", "capital"]
            },
            {
                "name": "JSON Response",
                "message": "Please respond with a JSON object: {\"name\": \"your_name\", \"description\": \"brief_description\"}",
                "expected_keywords": ["name", "description"]
            }
        ]

        successful_tests = 0
        total_tests = len(test_scenarios)

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📤 Test {i}/{total_tests}: {scenario['name']}")
            print(f"Message: {scenario['message']}")

            try:
                response = llm.invoke(scenario['message'])
                response_content = response.content.lower()

                print(f"📥 Response: {response.content[:150]}...")

                # Check if response contains expected keywords
                found_keywords = [kw for kw in scenario['expected_keywords']
                                if kw.lower() in response_content]

                if found_keywords or len(response.content) > 10:  # Got a reasonable response
                    print(f"✅ Test {i} passed")
                    if found_keywords:
                        print(f"   Found keywords: {found_keywords}")
                    successful_tests += 1
                else:
                    print(f"❌ Test {i} failed - no meaningful response")

            except Exception as e:
                print(f"❌ Test {i} failed: {e}")

            print("-" * 40)

        success_rate = (successful_tests / total_tests) * 100
        print(f"\n📊 Chat Test Summary: {successful_tests}/{total_tests} tests passed ({success_rate:.1f}%)")

        if successful_tests >= total_tests * 0.7:  # 70% success rate
            print("✅ SiliconFlow chat test successful!")
            return True
        else:
            print("❌ SiliconFlow chat test failed")
            return False

    except Exception as e:
        print(f"❌ SiliconFlow chat test failed: {e}")
        return False

# Removed test_langgraph_workflow function as requested

def main():
    """Run all tests."""
    print("🧪 Testing DeepSeek Integration")
    print("=" * 40)
    
    tests = [
        test_environment_variables,
        test_imports,
        test_configuration,
        test_llm_initialization,
        test_json_parsing,
        test_siliconflow_chat,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DeepSeek integration is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
