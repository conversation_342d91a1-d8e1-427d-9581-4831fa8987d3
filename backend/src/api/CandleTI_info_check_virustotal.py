#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VirusTotal威胁情报查询工具

该模块提供多种VirusTotal威胁情报查询功能：
1. IP地址查询 - IP声誉、恶意软件检测、地理位置信息
2. 域名查询 - 域名声誉、DNS记录、恶意软件关联
3. URL查询 - URL安全性、恶意软件检测、钓鱼检测

作者: Security Team
版本: 1.0.0
"""

import argparse
import json
import sys
import time
import hashlib
import base64
from typing import Dict, Any, Optional
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError
from urllib.parse import urlencode, urlparse
import ipaddress
import re


class VirusTotalThreatIntelligence:
    """
    VirusTotal威胁情报查询类
    
    提供IP地址、域名和URL的威胁情报查询功能，包括恶意软件检测、
    声誉评分、安全分析等专业威胁情报信息。
    """
    
    def __init__(self, api_key: str):
        """
        初始化VirusTotal威胁情报查询器
        
        Args:
            api_key: VirusTotal API密钥
        """
        self.api_key = api_key
        self.base_url = "https://www.virustotal.com/api/v3"
        self.headers = {
            "X-Apikey": self.api_key,
            "Accept": "application/json"
        }
    
    def _validate_ip(self, ip_address: str) -> bool:
        """
        验证IP地址格式
        
        Args:
            ip_address: 待验证的IP地址
            
        Returns:
            bool: IP地址是否有效
        """
        try:
            ipaddress.ip_address(ip_address)
            return True
        except ValueError:
            return False
    
    def _validate_domain(self, domain: str) -> bool:
        """
        验证域名格式
        
        Args:
            domain: 待验证的域名
            
        Returns:
            bool: 域名是否有效
        """
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+'  # 域名部分
            r'[a-zA-Z]{2,}$'  # 顶级域名
        )
        return bool(domain_pattern.match(domain))
    
    def _validate_url(self, url: str) -> bool:
        """
        验证URL格式
        
        Args:
            url: 待验证的URL
            
        Returns:
            bool: URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def _make_http_request(self, url: str, method: str = "GET", data: Optional[bytes] = None) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求并返回JSON响应
        
        Args:
            url: 请求URL
            method: HTTP方法
            data: 请求数据（用于POST请求）
            
        Returns:
            Dict: JSON响应数据，失败时返回None
        """
        try:
            print(f"VirusTotal API {url}")
            req = Request(url, headers=self.headers, data=data, method=method)
            with urlopen(req, timeout=30) as response:
                response_data = response.read().decode('utf-8')
                return json.loads(response_data)
        except (URLError, HTTPError, json.JSONDecodeError, Exception) as e:
            print(f"VirusTotal API {url}请求失败: {e}")
            return None
    
    def _url_to_id(self, url: str) -> str:
        """
        将URL转换为VirusTotal的URL ID
        
        Args:
            url: 目标URL
            
        Returns:
            str: Base64编码的URL ID
        """
        return base64.urlsafe_b64encode(url.encode()).decode().strip("=")
    
    def _calculate_threat_level(self, malicious: int, suspicious: int, total: int) -> str:
        """
        根据检测结果计算威胁等级
        
        Args:
            malicious: 恶意检测数量
            suspicious: 可疑检测数量
            total: 总检测引擎数量
            
        Returns:
            str: 威胁等级描述
        """
        if total == 0:
            return "🔵 未知"
        
        malicious_rate = malicious / total
        suspicious_rate = suspicious / total
        
        if malicious_rate >= 0.1:  # 10%以上引擎检测为恶意
            return "🔴 高危"
        elif malicious_rate >= 0.05 or suspicious_rate >= 0.2:  # 5%以上恶意或20%以上可疑
            return "🟡 中危"
        elif malicious_rate > 0 or suspicious_rate > 0:
            return "🟠 低危"
        else:
            return "🟢 安全"
    
    def _get_relations(self, target: str, target_type: str, limit: int = 10) -> Dict[str, Any]:
        """
        获取目标的关联信息（Relations）
        
        Args:
            target: 目标（IP、域名或URL）
            target_type: 目标类型 ("ip", "domain", "url")
            limit: 返回关联信息的数量限制
            
        Returns:
            Dict: 包含关联信息的字典
        """
        relations_result = {
            "communicating_files": [],
            "downloaded_files": [],
            "referrer_files": [],
            "urls": [],
            "subdomains": [],
            "resolutions": [],
            "error": None
        }
        
        try:
            # 根据目标类型构建不同的关联查询URL
            if target_type == "ip":
                relations_endpoints = {
                    "communicating_files": f"{self.base_url}/ip_addresses/{target}/communicating_files",
                    "downloaded_files": f"{self.base_url}/ip_addresses/{target}/downloaded_files",
                    "referrer_files": f"{self.base_url}/ip_addresses/{target}/referrer_files",
                    "urls": f"{self.base_url}/ip_addresses/{target}/urls",
                    "resolutions": f"{self.base_url}/ip_addresses/{target}/resolutions"
                }
            elif target_type == "domain":
                relations_endpoints = {
                    "communicating_files": f"{self.base_url}/domains/{target}/communicating_files",
                    "downloaded_files": f"{self.base_url}/domains/{target}/downloaded_files",
                    "referrer_files": f"{self.base_url}/domains/{target}/referrer_files",
                    "urls": f"{self.base_url}/domains/{target}/urls",
                    "subdomains": f"{self.base_url}/domains/{target}/subdomains",
                    "resolutions": f"{self.base_url}/domains/{target}/resolutions"
                }
            elif target_type == "url":
                url_id = self._url_to_id(target)
                relations_endpoints = {
                    "communicating_files": f"{self.base_url}/urls/{url_id}/communicating_files",
                    "downloaded_files": f"{self.base_url}/urls/{url_id}/downloaded_files",
                    "referrer_files": f"{self.base_url}/urls/{url_id}/referrer_files"
                }
            else:
                relations_result["error"] = f"不支持的目标类型: {target_type}"
                return relations_result
            
            # 查询各种关联信息
            for relation_type, endpoint in relations_endpoints.items():
                try:
                    url_with_limit = f"{endpoint}?limit={limit}"
                    data = self._make_http_request(url_with_limit)
                    
                    if data and 'data' in data:
                        relation_items = []
                        for item in data['data'][:limit]:  # 限制返回数量
                            if relation_type in ["communicating_files", "downloaded_files", "referrer_files"]:
                                # 文件相关信息
                                file_info = {
                                    "sha256": item.get('id', '未知'),
                                    "names": item.get('attributes', {}).get('names', [])[:3],  # 前3个文件名
                                    "size": item.get('attributes', {}).get('size', 0),
                                    "type_description": item.get('attributes', {}).get('type_description', '未知'),
                                    "last_analysis_stats": item.get('attributes', {}).get('last_analysis_stats', {})
                                }
                                relation_items.append(file_info)
                            elif relation_type == "urls":
                                # URL相关信息
                                url_info = {
                                    "url": item.get('id', '未知'),
                                    "last_analysis_stats": item.get('attributes', {}).get('last_analysis_stats', {}),
                                    "title": item.get('attributes', {}).get('title', '未知')[:50]  # 限制标题长度
                                }
                                relation_items.append(url_info)
                            elif relation_type == "subdomains":
                                # 子域名信息
                                subdomain_info = {
                                    "subdomain": item.get('id', '未知'),
                                    "last_analysis_stats": item.get('attributes', {}).get('last_analysis_stats', {})
                                }
                                relation_items.append(subdomain_info)
                            elif relation_type == "resolutions":
                                # DNS解析信息
                                resolution_info = {
                                    "ip_address": item.get('attributes', {}).get('ip_address', '未知'),
                                    "host_name": item.get('attributes', {}).get('host_name', '未知'),
                                    "date": item.get('attributes', {}).get('date', '未知')
                                }
                                relation_items.append(resolution_info)
                        
                        relations_result[relation_type] = relation_items
                    
                except Exception as e:
                    print(f"获取{relation_type}关联信息失败: {e}")
                    relations_result[relation_type] = []
                    
        except Exception as e:
            relations_result["error"] = f"获取关联信息异常: {str(e)}"
        
        return relations_result
    
    def _print_relations(self, relations: Dict[str, Any]):
        """
        格式化打印关联信息
        
        Args:
            relations: 关联信息字典
        """
        print(f"\n🔗 关联信息分析:")
        
        if relations.get('error'):
            print(f"   ❌ 获取关联信息失败: {relations['error']}")
            return
        
        # 统计关联信息总数
        total_relations = sum(len(relations.get(key, [])) for key in relations.keys() if key != 'error')
        if total_relations == 0:
            print(f"   📭 未发现相关关联信息")
            return
        
        print(f"   📊 发现 {total_relations} 个关联项目")
        
        # 显示通信文件
        if relations.get('communicating_files'):
            print(f"\n   📁 通信文件 ({len(relations['communicating_files'])} 个):")
            for i, file_info in enumerate(relations['communicating_files'][:5], 1):
                stats = file_info.get('last_analysis_stats', {})
                malicious = stats.get('malicious', 0)
                total = sum(stats.values()) if stats else 0
                threat_indicator = "🔴" if malicious > 0 else "🟢"
                print(f"     {i}. {threat_indicator} SHA256: {file_info.get('sha256', '未知')[:16]}...")
                if file_info.get('names'):
                    print(f"        📝 文件名: {', '.join(file_info['names'])}")
                print(f"        📊 检测: {malicious}/{total} 恶意")
        
        # 显示下载文件
        if relations.get('downloaded_files'):
            print(f"\n   📥 下载文件 ({len(relations['downloaded_files'])} 个):")
            for i, file_info in enumerate(relations['downloaded_files'][:5], 1):
                stats = file_info.get('last_analysis_stats', {})
                malicious = stats.get('malicious', 0)
                total = sum(stats.values()) if stats else 0
                threat_indicator = "🔴" if malicious > 0 else "🟢"
                print(f"     {i}. {threat_indicator} SHA256: {file_info.get('sha256', '未知')[:16]}...")
                if file_info.get('names'):
                    print(f"        📝 文件名: {', '.join(file_info['names'])}")
                print(f"        📊 检测: {malicious}/{total} 恶意")
        
        # 显示引用文件
        if relations.get('referrer_files'):
            print(f"\n   🔗 引用文件 ({len(relations['referrer_files'])} 个):")
            for i, file_info in enumerate(relations['referrer_files'][:5], 1):
                stats = file_info.get('last_analysis_stats', {})
                malicious = stats.get('malicious', 0)
                total = sum(stats.values()) if stats else 0
                threat_indicator = "🔴" if malicious > 0 else "🟢"
                print(f"     {i}. {threat_indicator} SHA256: {file_info.get('sha256', '未知')[:16]}...")
                if file_info.get('names'):
                    print(f"        📝 文件名: {', '.join(file_info['names'])}")
                print(f"        📊 检测: {malicious}/{total} 恶意")
        
        # 显示关联URL
        if relations.get('urls'):
            print(f"\n   🌐 关联URL ({len(relations['urls'])} 个):")
            for i, url_info in enumerate(relations['urls'][:5], 1):
                stats = url_info.get('last_analysis_stats', {})
                malicious = stats.get('malicious', 0)
                total = sum(stats.values()) if stats else 0
                threat_indicator = "🔴" if malicious > 0 else "🟢"
                url = url_info.get('url', '未知')
                display_url = url[:50] + '...' if len(url) > 50 else url
                print(f"     {i}. {threat_indicator} {display_url}")
                if url_info.get('title') and url_info['title'] != '未知':
                    print(f"        📄 标题: {url_info['title']}")
                print(f"        📊 检测: {malicious}/{total} 恶意")
        
        # 显示子域名
        if relations.get('subdomains'):
            print(f"\n   🌿 子域名 ({len(relations['subdomains'])} 个):")
            for i, subdomain_info in enumerate(relations['subdomains'][:5], 1):
                stats = subdomain_info.get('last_analysis_stats', {})
                malicious = stats.get('malicious', 0)
                total = sum(stats.values()) if stats else 0
                threat_indicator = "🔴" if malicious > 0 else "🟢"
                print(f"     {i}. {threat_indicator} {subdomain_info.get('subdomain', '未知')}")
                print(f"        📊 检测: {malicious}/{total} 恶意")
        
        # 显示DNS解析记录
        if relations.get('resolutions'):
            print(f"\n   🔍 DNS解析记录 ({len(relations['resolutions'])} 个):")
            for i, resolution_info in enumerate(relations['resolutions'][:5], 1):
                ip_addr = resolution_info.get('ip_address', '未知')
                hostname = resolution_info.get('host_name', '未知')
                date = resolution_info.get('date', '未知')
                print(f"     {i}. 🌐 {hostname} ➜ {ip_addr}")
                print(f"        📅 解析时间: {date}")
        
        # 如果有更多关联信息，显示提示
        if total_relations > 25:  # 如果总数超过显示的数量
            print(f"\n   💡 提示: 还有更多关联信息未显示，总计 {total_relations} 个关联项目")
    
    def query_ip(self, ip_address: str) -> Dict[str, Any]:
        """
        查询IP地址威胁情报
        
        Args:
            ip_address: 目标IP地址
            
        Returns:
            Dict: 包含IP威胁情报的字典
        """
        result = {
            "source": "VirusTotal",
            "type": "IP",
            "target": ip_address,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_ip(ip_address):
            result["error"] = "无效的IP地址格式"
            return result
        
        try:
            url = f"{self.base_url}/ip_addresses/{ip_address}"
            data = self._make_http_request(url)
            
            if data and 'data' in data:
                ip_data = data['data']
                attributes = ip_data.get('attributes', {})
                stats = attributes.get('last_analysis_stats', {})
                
                malicious = stats.get('malicious', 0)
                suspicious = stats.get('suspicious', 0)
                harmless = stats.get('harmless', 0)
                undetected = stats.get('undetected', 0)
                total_engines = malicious + suspicious + harmless + undetected
                
                # 获取关联信息
                relations = self._get_relations(ip_address, "ip")
                
                result["status"] = "success"
                result["data"] = {
                    "ip": ip_address,
                    "country": attributes.get('country', '未知'),
                    "asn": attributes.get('asn', '未知'),
                    "as_owner": attributes.get('as_owner', '未知'),
                    "reputation": attributes.get('reputation', 0),
                    "last_analysis_date": attributes.get('last_analysis_date', '未知'),
                    "detection_stats": {
                        "malicious": malicious,
                        "suspicious": suspicious,
                        "harmless": harmless,
                        "undetected": undetected,
                        "total_engines": total_engines
                    },
                    "threat_level": self._calculate_threat_level(malicious, suspicious, total_engines),
                    "network": attributes.get('network', '未知'),
                    "whois": attributes.get('whois', '未知')[:200] + '...' if attributes.get('whois') else '未知',
                    "relations": relations
                }
            else:
                result["error"] = "无法获取VirusTotal IP数据"
                
        except Exception as e:
            result["error"] = f"VirusTotal IP查询异常: {str(e)}"
        
        return result
    
    def query_domain(self, domain: str) -> Dict[str, Any]:
        """
        查询域名威胁情报
        
        Args:
            domain: 目标域名
            
        Returns:
            Dict: 包含域名威胁情报的字典
        """
        result = {
            "source": "VirusTotal",
            "type": "Domain",
            "target": domain,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_domain(domain):
            result["error"] = "无效的域名格式"
            return result
        
        try:
            url = f"{self.base_url}/domains/{domain}"
            data = self._make_http_request(url)
            
            if data and 'data' in data:
                domain_data = data['data']
                attributes = domain_data.get('attributes', {})
                stats = attributes.get('last_analysis_stats', {})
                
                malicious = stats.get('malicious', 0)
                suspicious = stats.get('suspicious', 0)
                harmless = stats.get('harmless', 0)
                undetected = stats.get('undetected', 0)
                total_engines = malicious + suspicious + harmless + undetected
                
                # 获取关联信息
                relations = self._get_relations(domain, "domain")
                
                result["status"] = "success"
                result["data"] = {
                    "domain": domain,
                    "registrar": attributes.get('registrar', '未知'),
                    "creation_date": attributes.get('creation_date', '未知'),
                    "last_update_date": attributes.get('last_update_date', '未知'),
                    "expiration_date": attributes.get('expiration_date', '未知'),
                    "reputation": attributes.get('reputation', 0),
                    "last_analysis_date": attributes.get('last_analysis_date', '未知'),
                    "detection_stats": {
                        "malicious": malicious,
                        "suspicious": suspicious,
                        "harmless": harmless,
                        "undetected": undetected,
                        "total_engines": total_engines
                    },
                    "threat_level": self._calculate_threat_level(malicious, suspicious, total_engines),
                    "categories": attributes.get('categories', {}),
                    "dns_records": {
                        "A": attributes.get('last_dns_records', []),
                        "MX": attributes.get('last_dns_records_mx', []),
                        "NS": attributes.get('last_dns_records_ns', [])
                    },
                    "whois": attributes.get('whois', '未知')[:200] + '...' if attributes.get('whois') else '未知',
                    "relations": relations
                }
            else:
                result["error"] = "无法获取VirusTotal域名数据"
                
        except Exception as e:
            result["error"] = f"VirusTotal域名查询异常: {str(e)}"
        
        return result
    
    def query_url(self, url: str) -> Dict[str, Any]:
        """
        查询URL威胁情报
        
        Args:
            url: 目标URL
            
        Returns:
            Dict: 包含URL威胁情报的字典
        """
        result = {
            "source": "VirusTotal",
            "type": "URL",
            "target": url,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_url(url):
            result["error"] = "无效的URL格式"
            return result
        
        try:
            # 首先尝试获取已存在的URL分析结果
            url_id = self._url_to_id(url)
            get_url = f"{self.base_url}/urls/{url_id}"
            data = self._make_http_request(get_url)
            
            # 如果没有找到，提交URL进行分析
            if not data or 'error' in data:
                print(f"URL未找到历史记录，提交进行分析...")
                submit_url = f"{self.base_url}/urls"
                submit_data = urlencode({'url': url}).encode('utf-8')
                submit_headers = self.headers.copy()
                submit_headers['Content-Type'] = 'application/x-www-form-urlencoded'
                
                submit_req = Request(submit_url, headers=submit_headers, data=submit_data, method='POST')
                with urlopen(submit_req, timeout=30) as response:
                    submit_response = json.loads(response.read().decode('utf-8'))
                
                if 'data' in submit_response:
                    analysis_id = submit_response['data']['id']
                    print(f"URL已提交分析，等待结果... (ID: {analysis_id})")
                    
                    # 等待分析完成
                    for attempt in range(10):  # 最多等待10次
                        time.sleep(3)  # 等待3秒
                        analysis_url = f"{self.base_url}/analyses/{analysis_id}"
                        analysis_data = self._make_http_request(analysis_url)
                        
                        if analysis_data and 'data' in analysis_data:
                            if analysis_data['data']['attributes']['status'] == 'completed':
                                # 分析完成，获取结果
                                data = self._make_http_request(get_url)
                                break
                    else:
                        result["error"] = "URL分析超时，请稍后重试"
                        return result
            
            if data and 'data' in data:
                url_data = data['data']
                attributes = url_data.get('attributes', {})
                stats = attributes.get('last_analysis_stats', {})
                
                malicious = stats.get('malicious', 0)
                suspicious = stats.get('suspicious', 0)
                harmless = stats.get('harmless', 0)
                undetected = stats.get('undetected', 0)
                total_engines = malicious + suspicious + harmless + undetected
                
                # 获取关联信息
                relations = self._get_relations(url, "url")
                
                result["status"] = "success"
                result["data"] = {
                    "url": url,
                    "final_url": attributes.get('final_url', url),
                    "title": attributes.get('title', '未知'),
                    "last_analysis_date": attributes.get('last_analysis_date', '未知'),
                    "first_submission_date": attributes.get('first_submission_date', '未知'),
                    "times_submitted": attributes.get('times_submitted', 0),
                    "detection_stats": {
                        "malicious": malicious,
                        "suspicious": suspicious,
                        "harmless": harmless,
                        "undetected": undetected,
                        "total_engines": total_engines
                    },
                    "threat_level": self._calculate_threat_level(malicious, suspicious, total_engines),
                    "categories": attributes.get('categories', {}),
                    "threat_names": list(set([result.get('result', '') for result in attributes.get('last_analysis_results', {}).values() if result.get('category') == 'malicious' and result.get('result')]))[:5],  # 前5个威胁名称
                    "reputation": attributes.get('reputation', 0),
                    "relations": relations
                }
            else:
                result["error"] = "无法获取VirusTotal URL数据"
                
        except Exception as e:
            result["error"] = f"VirusTotal URL查询异常: {str(e)}"
        
        return result
    
    def comprehensive_query(self, target: str, target_type: str = "auto") -> Dict[str, Any]:
        """
        综合查询：根据目标类型自动选择查询方式或执行所有适用的查询
        
        Args:
            target: 目标（IP、域名或URL）
            target_type: 目标类型 ("auto", "ip", "domain", "url")
            
        Returns:
            Dict: 包含查询结果的综合报告
        """
        print(f"\n🔍 开始对目标 {target} 进行VirusTotal威胁情报分析...\n")
        
        results = {
            "target": target,
            "target_type": target_type,
            "timestamp": int(time.time()),
            "summary": {
                "total_queries": 0,
                "successful_queries": 0,
                "failed_queries": 0
            },
            "results": {}
        }
        
        # 自动检测目标类型
        if target_type == "auto":
            if self._validate_ip(target):
                target_type = "ip"
            elif self._validate_url(target):
                target_type = "url"
            elif self._validate_domain(target):
                target_type = "domain"
            else:
                results["results"]["error"] = {
                    "source": "VirusTotal",
                    "type": "Unknown",
                    "target": target,
                    "status": "error",
                    "data": {},
                    "error": "无法识别目标类型（IP、域名或URL）"
                }
                results["summary"]["total_queries"] = 1
                results["summary"]["failed_queries"] = 1
                return results
        
        # 根据类型执行相应查询
        if target_type == "ip":
            results["results"]["ip_analysis"] = self.query_ip(target)
            results["summary"]["total_queries"] = 1
        elif target_type == "domain":
            results["results"]["domain_analysis"] = self.query_domain(target)
            results["summary"]["total_queries"] = 1
        elif target_type == "url":
            results["results"]["url_analysis"] = self.query_url(target)
            results["summary"]["total_queries"] = 1
        
        # 统计成功和失败的查询
        for query_result in results["results"].values():
            if query_result.get("status") == "success":
                results["summary"]["successful_queries"] += 1
            else:
                results["summary"]["failed_queries"] += 1
        
        return results
    
    def print_formatted_result(self, result: Dict[str, Any], query_type: str = ""):
        """
        格式化打印查询结果
        
        Args:
            result: 查询结果字典
            query_type: 查询类型描述
        """
        print(f"\n{'='*70}")
        print(f"🛡️  {query_type or result.get('type', '未知')} 威胁情报分析结果")
        print(f"{'='*70}")
        print(f"🎯 目标: {result.get('target', '未知')}")
        print(f"📡 数据源: {result.get('source', '未知')}")
        print(f"📊 类型: {result.get('type', '未知')}")
        print(f"✅ 状态: {result.get('status', '未知')}")
        
        if result.get('status') == 'success' and result.get('data'):
            data = result['data']
            print(f"\n📋 威胁情报详情:")
            
            # 威胁等级
            if 'threat_level' in data:
                print(f"   ⚠️  威胁等级: {data['threat_level']}")
            
            # 检测统计
            if 'detection_stats' in data:
                stats = data['detection_stats']
                print(f"   🔍 检测统计: {stats['malicious']}/{stats['total_engines']} 恶意, {stats['suspicious']}/{stats['total_engines']} 可疑")
            
            # 其他详细信息
            for key, value in data.items():
                if key not in ['threat_level', 'detection_stats', 'relations']:
                    if isinstance(value, dict):
                        print(f"   • {key}:")
                        for sub_key, sub_value in value.items():
                            print(f"     - {sub_key}: {sub_value}")
                    elif isinstance(value, list):
                        if value:  # 只显示非空列表
                            print(f"   • {key}: {', '.join(map(str, value[:3]))}{'...' if len(value) > 3 else ''}")
                    else:
                        print(f"   • {key}: {value}")
            
            # 显示关联信息
            if 'relations' in data:
                self._print_relations(data['relations'])
        elif result.get('error'):
            print(f"❌ 错误: {result['error']}")
        
        print(f"{'='*70}\n")
    
    def print_comprehensive_report(self, results: Dict[str, Any]):
        """
        打印综合查询报告
        
        Args:
            results: 综合查询结果
        """
        print(f"\n{'🛡️ VirusTotal威胁情报综合分析报告':=^90}")
        print(f"\n🎯 分析目标: {results['target']}")
        print(f"📊 目标类型: {results['target_type'].upper()}")
        print(f"📈 查询统计: {results['summary']['successful_queries']}/{results['summary']['total_queries']} 成功")
        print(f"⏰ 分析时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(results['timestamp']))}")
        
        # 威胁评估总结
        threat_levels = []
        for query_result in results['results'].values():
            if query_result.get('status') == 'success' and 'threat_level' in query_result.get('data', {}):
                threat_levels.append(query_result['data']['threat_level'])
        
        if threat_levels:
            if any('🔴' in level for level in threat_levels):
                overall_threat = "🔴 高危"
            elif any('🟡' in level for level in threat_levels):
                overall_threat = "🟡 中危"
            elif any('🟠' in level for level in threat_levels):
                overall_threat = "🟠 低危"
            else:
                overall_threat = "🟢 安全"
            print(f"⚠️  综合威胁评估: {overall_threat}")
        
        print(f"\n{'详细分析结果':=^90}")
        
        for query_name, query_result in results['results'].items():
            self.print_formatted_result(query_result)


def main():
    """
    主函数：处理命令行参数并执行查询
    """
    # VirusTotal API密钥配置
    VIRUSTOTAL_API_KEY = "****************************************************************"  # 请替换为您的实际API密钥
    
    parser = argparse.ArgumentParser(
        description='VirusTotal威胁情报查询工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 %(prog)s *******                           # IP地址查询
  python3 %(prog)s google.com                        # 域名查询
  python3 %(prog)s https://example.com               # URL查询
  python3 %(prog)s ******* --type ip                # 指定类型查询
  python3 %(prog)s google.com --json                 # JSON格式输出
  python3 %(prog)s target --api-key YOUR_KEY         # 自定义API密钥
        """
    )
    
    parser.add_argument('target', help='目标（IP地址、域名或URL）')
    parser.add_argument('--api-key', help='VirusTotal API密钥', default=VIRUSTOTAL_API_KEY)
    parser.add_argument('--type', choices=['auto', 'ip', 'domain', 'url'], 
                       default='auto', help='目标类型（默认自动检测）')
    parser.add_argument('--json', action='store_true', help='以JSON格式输出结果')
    
    args = parser.parse_args()
    
    # 检查API密钥
    if not args.api_key:
        print("❌ 错误: 请提供有效的VirusTotal API密钥")
        print("   可以通过 --api-key 参数提供，或在脚本中配置 VIRUSTOTAL_API_KEY")
        sys.exit(1)
    
    # 创建查询器实例
    vt_intel = VirusTotalThreatIntelligence(api_key=args.api_key)
    
    # 执行查询
    if args.type != 'auto':
        # 单一类型查询
        if args.type == 'ip':
            result = vt_intel.query_ip(args.target)
        elif args.type == 'domain':
            result = vt_intel.query_domain(args.target)
        elif args.type == 'url':
            result = vt_intel.query_url(args.target)
        
        if args.json:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            vt_intel.print_formatted_result(result)
    else:
        # 综合查询（自动检测类型）
        results = vt_intel.comprehensive_query(args.target, args.type)
        if args.json:
            print(json.dumps(results, indent=2, ensure_ascii=False))
        else:
            vt_intel.print_comprehensive_report(results)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
        sys.exit(1)