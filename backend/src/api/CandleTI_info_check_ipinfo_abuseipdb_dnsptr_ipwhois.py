#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IP威胁情报查询工具

该模块提供多种IP地址威胁情报查询功能：
1. ipinfo.io - 基础地理位置和ISP信息
2. AbuseIPDB - 恶意IP评分和滥用报告
3. DNS PTR查询 - 反向DNS解析
4. ipwhois - ASN、注册商等详细信息

作者: Security Team
版本: 1.0.0
"""

import argparse
import json
import sys
import socket
from typing import Dict, Any, Optional
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError
from urllib.parse import urlencode
import dns.resolver
import dns.reversename
from ipwhois import IPWhois
import ipaddress


class IPThreatIntelligence:
    """
    IP威胁情报查询类
    
    提供多种IP地址威胁情报查询功能，包括地理位置、恶意评分、
    DNS信息和注册信息等。
    """
    
    def __init__(self, abuseipdb_api_key: Optional[str] = None):
        """
        初始化IP威胁情报查询器
        
        Args:
            abuseipdb_api_key: AbuseIPDB API密钥（可选）
        """
        self.abuseipdb_api_key = abuseipdb_api_key
        self.ipinfo_base_url = "https://ipinfo.io"
        self.abuseipdb_base_url = "https://api.abuseipdb.com/api/v2"
    
    def _validate_ip(self, ip_address: str) -> bool:
        """
        验证IP地址格式
        
        Args:
            ip_address: 待验证的IP地址
            
        Returns:
            bool: IP地址是否有效
        """
        try:
            ipaddress.ip_address(ip_address)
            return True
        except ValueError:
            return False
    
    def _make_http_request(self, url: str, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求并返回JSON响应
        
        Args:
            url: 请求URL
            headers: 请求头（可选）
            
        Returns:
            Dict: JSON响应数据，失败时返回None
        """
        try:
            req = Request(url, headers=headers or {})
            with urlopen(req, timeout=10) as response:
                data = response.read().decode('utf-8')
                return json.loads(data)
        except (URLError, HTTPError, json.JSONDecodeError, Exception) as e:
            print(f"HTTP请求失败: {e}")
            return None
    
    def query_ipinfo(self, ip_address: str) -> Dict[str, Any]:
        """
        使用ipinfo.io查询IP基础信息
        
        Args:
            ip_address: 目标IP地址
            
        Returns:
            Dict: 包含地理位置、ISP等信息的字典
        """
        result = {
            "source": "ipinfo.io",
            "ip": ip_address,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_ip(ip_address):
            result["error"] = "无效的IP地址格式"
            return result
        
        try:
            url = f"{self.ipinfo_base_url}/{ip_address}/json"
            data = self._make_http_request(url)
            
            if data:
                result["status"] = "success"
                result["data"] = {
                    "ip": data.get("ip", ip_address),
                    "city": data.get("city", "未知"),
                    "region": data.get("region", "未知"),
                    "country": data.get("country", "未知"),
                    "location": data.get("loc", "未知"),
                    "organization": data.get("org", "未知"),
                    "postal": data.get("postal", "未知"),
                    "timezone": data.get("timezone", "未知")
                }
            else:
                result["error"] = "无法获取ipinfo.io数据"
                
        except Exception as e:
            result["error"] = f"ipinfo.io查询异常: {str(e)}"
        
        return result
    
    def query_abuseipdb(self, ip_address: str) -> Dict[str, Any]:
        """
        使用AbuseIPDB查询IP恶意评分
        
        Args:
            ip_address: 目标IP地址
            
        Returns:
            Dict: 包含恶意评分和滥用报告的字典
        """
        result = {
            "source": "AbuseIPDB",
            "ip": ip_address,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_ip(ip_address):
            result["error"] = "无效的IP地址格式"
            return result
        
        if not self.abuseipdb_api_key:
            result["error"] = "未提供AbuseIPDB API密钥"
            return result
        
        try:
            params = {
                'ipAddress': ip_address,
                'maxAgeInDays': 90,
                'verbose': ''
            }
            
            url = f"{self.abuseipdb_base_url}/check?{urlencode(params)}"
            headers = {
                'Key': self.abuseipdb_api_key,
                'Accept': 'application/json'
            }
            
            data = self._make_http_request(url, headers)
            
            if data and 'data' in data:
                abuse_data = data['data']
                result["status"] = "success"
                result["data"] = {
                    "ip": abuse_data.get("ipAddress", ip_address),
                    "abuse_confidence": abuse_data.get("abuseConfidencePercentage", 0),
                    "country_code": abuse_data.get("countryCode", "未知"),
                    "usage_type": abuse_data.get("usageType", "未知"),
                    "isp": abuse_data.get("isp", "未知"),
                    "domain": abuse_data.get("domain", "未知"),
                    "total_reports": abuse_data.get("totalReports", 0),
                    "num_distinct_users": abuse_data.get("numDistinctUsers", 0),
                    "last_reported_at": abuse_data.get("lastReportedAt", "从未报告")
                }
            else:
                result["error"] = "无法获取AbuseIPDB数据"
                
        except Exception as e:
            result["error"] = f"AbuseIPDB查询异常: {str(e)}"
        
        return result
    
    def query_dns_ptr(self, ip_address: str) -> Dict[str, Any]:
        """
        使用dnspython进行PTR/DNS查询
        
        Args:
            ip_address: 目标IP地址
            
        Returns:
            Dict: 包含PTR记录和DNS信息的字典
        """
        result = {
            "source": "DNS PTR",
            "ip": ip_address,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_ip(ip_address):
            result["error"] = "无效的IP地址格式"
            return result
        
        try:
            # PTR查询（反向DNS）
            ptr_records = []
            try:
                reversed_dns = dns.reversename.from_address(ip_address)
                answers = dns.resolver.resolve(reversed_dns, "PTR")
                ptr_records = [str(rdata) for rdata in answers]
            except Exception:
                ptr_records = []
            
            # 如果有PTR记录，尝试正向DNS查询验证
            forward_dns = []
            if ptr_records:
                for hostname in ptr_records:
                    try:
                        hostname = hostname.rstrip('.')
                        answers = dns.resolver.resolve(hostname, "A")
                        forward_dns.extend([str(rdata) for rdata in answers])
                    except Exception:
                        continue
            
            result["status"] = "success"
            result["data"] = {
                "ip": ip_address,
                "ptr_records": ptr_records,
                "forward_dns_verification": forward_dns,
                "ptr_count": len(ptr_records),
                "dns_verified": ip_address in forward_dns if forward_dns else False
            }
            
        except Exception as e:
            result["error"] = f"DNS PTR查询异常: {str(e)}"
        
        return result
    
    def query_ipwhois(self, ip_address: str) -> Dict[str, Any]:
        """
        使用ipwhois查询ASN和注册信息
        
        Args:
            ip_address: 目标IP地址
            
        Returns:
            Dict: 包含ASN、注册商等详细信息的字典
        """
        result = {
            "source": "ipwhois",
            "ip": ip_address,
            "status": "error",
            "data": {},
            "error": None
        }
        
        if not self._validate_ip(ip_address):
            result["error"] = "无效的IP地址格式"
            return result
        
        try:
            obj = IPWhois(ip_address)
            whois_data = obj.lookup_rdap(depth=1)
            
            result["status"] = "success"
            result["data"] = {
                "ip": ip_address,
                "asn": whois_data.get("asn", "未知"),
                "asn_description": whois_data.get("asn_description", "未知"),
                "asn_country_code": whois_data.get("asn_country_code", "未知"),
                "asn_date": whois_data.get("asn_date", "未知"),
                "network_name": whois_data.get("network", {}).get("name", "未知"),
                "network_cidr": whois_data.get("network", {}).get("cidr", "未知"),
                "network_country": whois_data.get("network", {}).get("country", "未知"),
                "network_start_address": whois_data.get("network", {}).get("start_address", "未知"),
                "network_end_address": whois_data.get("network", {}).get("end_address", "未知")
            }
            
        except Exception as e:
            result["error"] = f"ipwhois查询异常: {str(e)}"
        
        return result
    
    def comprehensive_query(self, ip_address: str) -> Dict[str, Any]:
        """
        综合查询：执行所有四种查询方式
        
        Args:
            ip_address: 目标IP地址
            
        Returns:
            Dict: 包含所有查询结果的综合报告
        """
        print(f"\n🔍 开始对IP地址 {ip_address} 进行综合威胁情报分析...\n")
        
        results = {
            "target_ip": ip_address,
            "timestamp": None,
            "summary": {
                "total_queries": 4,
                "successful_queries": 0,
                "failed_queries": 0
            },
            "results": {
                "ipinfo": self.query_ipinfo(ip_address),
                "abuseipdb": self.query_abuseipdb(ip_address),
                "dns_ptr": self.query_dns_ptr(ip_address),
                "ipwhois": self.query_ipwhois(ip_address)
            }
        }
        
        # 统计成功和失败的查询
        for query_result in results["results"].values():
            if query_result["status"] == "success":
                results["summary"]["successful_queries"] += 1
            else:
                results["summary"]["failed_queries"] += 1
        
        return results
    
    def print_formatted_result(self, result: Dict[str, Any], query_type: str = ""):
        """
        格式化打印查询结果
        
        Args:
            result: 查询结果字典
            query_type: 查询类型描述
        """
        print(f"\n{'='*60}")
        print(f"📊 {query_type or result.get('source', '未知')} 查询结果")
        print(f"{'='*60}")
        print(f"🎯 目标IP: {result.get('ip', '未知')}")
        print(f"📡 数据源: {result.get('source', '未知')}")
        print(f"✅ 状态: {result.get('status', '未知')}")
        
        if result.get('status') == 'success' and result.get('data'):
            print(f"\n📋 详细信息:")
            for key, value in result['data'].items():
                print(f"   • {key}: {value}")
        elif result.get('error'):
            print(f"❌ 错误: {result['error']}")
        
        print(f"{'='*60}\n")
    
    def print_comprehensive_report(self, results: Dict[str, Any]):
        """
        打印综合查询报告
        
        Args:
            results: 综合查询结果
        """
        print(f"\n{'🛡️ IP威胁情报综合分析报告':=^80}")
        print(f"\n🎯 目标IP地址: {results['target_ip']}")
        print(f"📊 查询统计: {results['summary']['successful_queries']}/{results['summary']['total_queries']} 成功")
        
        # 威胁评估
        abuse_result = results['results'].get('abuseipdb', {})
        if abuse_result.get('status') == 'success':
            confidence = abuse_result['data'].get('abuse_confidence', 0)
            if confidence >= 75:
                threat_level = "🔴 高危"
            elif confidence >= 25:
                threat_level = "🟡 中危"
            else:
                threat_level = "🟢 低危"
            print(f"⚠️  威胁等级: {threat_level} (恶意置信度: {confidence}%)")
        
        print(f"\n{'详细查询结果':=^80}")
        
        for query_name, query_result in results['results'].items():
            self.print_formatted_result(query_result)


def main():
    """
    主函数：处理命令行参数并执行查询
    """
    parser = argparse.ArgumentParser(
        description='IP威胁情报查询工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 %(prog)s *******                    # 综合查询
  python3 %(prog)s ******* --ipinfo           # 仅ipinfo查询
  python3 %(prog)s ******* --abuseipdb --api-key YOUR_KEY  # AbuseIPDB查询
  python3 %(prog)s ******* --dns              # DNS PTR查询
  python3 %(prog)s ******* --whois            # ipwhois查询
        """
    )
    
    parser.add_argument('ip', help='目标IP地址')
    parser.add_argument('--api-key', help='AbuseIPDB API密钥')
    
    # 查询方式选项
    query_group = parser.add_mutually_exclusive_group()
    query_group.add_argument('--ipinfo', action='store_true', help='仅执行ipinfo.io查询')
    query_group.add_argument('--abuseipdb', action='store_true', help='仅执行AbuseIPDB查询')
    query_group.add_argument('--dns', action='store_true', help='仅执行DNS PTR查询')
    query_group.add_argument('--whois', action='store_true', help='仅执行ipwhois查询')
    
    parser.add_argument('--json', action='store_true', help='以JSON格式输出结果')
    
    args = parser.parse_args()
    
    # 创建查询器实例
    abuseip_key = args.api_key if args.api_key else "58fadb5ff4f08899a62e8bad51f38f26cbd88477de5b41548e56050256b3aaa84c5a84fe96653208"
    intel = IPThreatIntelligence(abuseipdb_api_key=abuseip_key)
    
    # 根据参数执行相应查询
    if args.ipinfo:
        result = intel.query_ipinfo(args.ip)
        if args.json:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            intel.print_formatted_result(result, "ipinfo.io")
    
    elif args.abuseipdb:
        result = intel.query_abuseipdb(args.ip)
        if args.json:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            intel.print_formatted_result(result, "AbuseIPDB")
    
    elif args.dns:
        result = intel.query_dns_ptr(args.ip)
        if args.json:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            intel.print_formatted_result(result, "DNS PTR")
    
    elif args.whois:
        result = intel.query_ipwhois(args.ip)
        if args.json:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            intel.print_formatted_result(result, "ipwhois")
    
    else:
        # 默认执行综合查询
        results = intel.comprehensive_query(args.ip)
        if args.json:
            print(json.dumps(results, indent=2, ensure_ascii=False))
        else:
            intel.print_comprehensive_report(results)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
        sys.exit(1)