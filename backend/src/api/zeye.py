#!/usr/bin/env python
# -*- coding: utf-8 -*-
import base64
import requests

class ZoomEyeV2:
    def __init__(self, api_key="", api_url="https://api.zoomeye.org/v2/search"):
        self.api_key = api_key
        self.api_url = api_url
        self.session = requests.Session()
        self.session.trust_env = False
        self.export_field_words = [
            'ip', 'domain', 'url', 'ssl.jarm', 'hostname', 'port', 'service', 'title', 'header',
            'banner', 'body', 'update_time', 'continent.name', 'country.name', 'province.name',
            'city.name', 'isp.name', 'organization.name', 'lon', 'lat', 'asn', 'ssl', 'protocol',
            'os', 'device', 'rdns', 'product', 'header_hash', 'body_hash']

    def __finish__(self):
        self.session.close()

    def search_less(self, query):
        """
        说明： 该接口非多线程安全，多线程环境建议每个线程初始化一个类的实例。
              查询返回的字段少(减少网络传输，加速返回效率)，用于验证指纹规则。
        """
        items = ['ip', 'port', 'service', 'title', 'organization.name']
        headers = {'API-KEY': self.api_key}
        qbase64 = base64.b64encode(query.encode('utf-8')).decode()
        body = {
            'qbase64': qbase64,
            'page': 1,
            'pagesize': 1,
            'sub_type': "v4",
            'fields': ','.join(items)
        }
        try:
            resp = self.session.post(self.api_url, headers=headers, json=body, timeout=20)
            resp.raise_for_status()
            json_ret = resp.json()
            return json_ret['total'], json_ret['data']
        except requests.exceptions.RequestException as e:
            print(f"API请求({query})失败: {str(e)}")
            return 0, []

    def search(self, query, page=1, pagesize=10):
        """
        说明： 该接口非多线程安全，多线程环境建议每个线程初始化一个类的实例。
        """
        headers = {'API-KEY': self.api_key}
        sub_type = "web" if query.startswith('domain') else "v4"
        qbase64 = base64.b64encode(query.encode('utf-8')).decode()        
        body = {
            'qbase64': qbase64,
            'page': page,
            'pagesize': pagesize,
            'sub_type': sub_type,
            'fields': ','.join(self.export_field_words)
        }
        try:
            resp = self.session.post(self.api_url, headers=headers, json=body, timeout=20)
            resp.raise_for_status()
            data_list = []
            total = resp.json()['total']
            if resp.json()['data'] is not None:
                for item in resp.json()['data']:
                    ssl_jarm = item.get('ssl.jarm', '')
                    if ssl_jarm:
                        item.pop('ssl.jarm')
                        item['ssl_jarm'] = ssl_jarm
                    continent_name = item.get('continent.name', '')
                    if continent_name:
                        item.pop('continent.name')
                        item['continent_name'] = continent_name
                    country_name = item.get('country.name', '')
                    if country_name:
                        item.pop('country.name')
                        item['country_name'] = country_name
                    province_name = item.get('province.name', '')
                    if province_name:
                        item.pop('province.name')
                        item['province_name'] = province_name
                    city_name = item.get('city.name', '')
                    if city_name:
                        item.pop('city.name')
                        item['city_name'] = city_name
                    isp_name = item.get('isp.name', '')
                    if isp_name:
                        item.pop('isp.name')
                        item['isp_name'] = isp_name
                    organization_name = item.get('organization.name', '')
                    if organization_name:
                        item.pop('organization.name')
                        item['organization_name'] = organization_name
                    data_list.append(item)
            return total, data_list
        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {str(e)}")
            return 0, []


if __name__ == '__main__':
    print("_------_-_-_-_------_")
    zoomeye_v2 = ZoomEyeV2('25B49348-8Ca2-5e864-9045-b9ef9d7c690')
    querys = 'ip="*************" && port=2053'
    #result = zoomeye_v2.search(querys)
    #print(json.dumps(result))

    q2 = 'initial-scale=1.0\">' # 164097491
    # q2 = 'width=device-width,' #168854682
    count, result = zoomeye_v2.search_less(q2)
    print(count)

