import dns.resolver
import argparse

def get_domain_info(domain: str) -> dict:
    dns_info = {}
    record = ["A", "AAAA", "MX", "NS", "TXT"]
    for t in record:
        try:
            answers = dns.resolver.resolve(domain, t)
            dns_info[t] = [str(r) for r in answers]
        except Exception as e:
            print(f"WARNING: Get domain {domain} with record {t} failed, Reason: {str(e)}")
            continue
    
    return dns_info

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='IOC检查工具')
    parser.add_argument('domain', nargs='?', default='', help='需要检测的DOMAIN值')
    args = parser.parse_args()
    domain_info = get_domain_info(args.domain)
    print("\n## 域名DNS信息查询结果\n")
    if not domain_info:
        print(f"未找到域名 `{args.domain}` 的DNS信息。")
        exit()

    print(f"**查询域名：** `{args.domain}`\n")

    for record_type, records in domain_info.items():
        print(f"### {record_type} 记录")
        if records:
            for record in records:
                print(record)
            print("\n")
        else:
            print("无\n")
