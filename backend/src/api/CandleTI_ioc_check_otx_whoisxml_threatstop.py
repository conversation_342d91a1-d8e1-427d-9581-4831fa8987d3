import argparse
import json
import requests
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional


def load_config(api_source='OTX',config_file='CandleTI_token_config.json'):
    try:
        with open(config_file) as f:
            config = json.load(f)
        if api_source == 'WhoisXML':
            return (
                config['WhoisXML']['API_KEY'],
                config['WhoisXML']['BASE_URL']
            )
        elif api_source == 'ThreatStop':
            return (
                config['ThreatStop']['API_KEY'],
                config['ThreatStop']['BASE_URL']
            )
        elif api_source == 'RSTCloud':
            return (
                config['RSTCloud']['API_KEY'],
                config['RSTCloud']['BASE_URL']
            )
        return (
            config['OTX']['API_KEY'],
            config['OTX']['OTX_SERVER'] + 'api/v1/indicators/'
        )
    except (KeyError, json.JSONDecodeError) as e:
        print(f'{{"error": "配置错误: {e}"}}')
        sys.exit(1)
    except FileNotFoundError:
        print('{{"error": "配置文件0TX-config.JSON未找到"}}')
        sys.exit(1)



def detect_ioc_type(value):
    import re
    # IP地址检测
    ip_pattern = r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'
    if re.match(ip_pattern, value):
        return 'ip'
    
    # 域名检测
    domain_pattern = r'^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'
    if re.match(domain_pattern, value):
        return 'domain'
    
    # URL检测
    if value.startswith(('http://', 'https://')):
        return 'url'
    
    # 哈希值检测
    hash_lengths = {
        32: 'md5',
        40: 'sha1',
        64: 'sha256'
    }
    clean_value = value.lower().replace(':', '').replace('-', '')
    if len(clean_value) in hash_lengths and re.match(r'^[a-f0-9]+$', clean_value):
        return hash_lengths[len(clean_value)]
    
    return None


def build_request_url(ioc_type, ioc_value, base_url):
    type_mapping = {
        'ip': 'IPv4',
        'domain': 'domain',
        'url': 'url',
        'md5': 'file',
        'sha1': 'file',
        'sha256': 'file'
    }
    try:
        return f'{base_url}{type_mapping[ioc_type]}/{ioc_value}/general'
    except KeyError:
        print(f'{"error": "不支持的IOC类型: {ioc_type}"}')
        sys.exit(1)


def fetch_ioc_data(api_key, url, headers=None):
    headers = headers or {'X-OTX-API-KEY': api_key, 'Accept': 'application/json'}
    try:
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f'{{"error": "API请求失败", "details": "{str(e)}, url:{url}"}}')
        #sys.exit(1)

def fetch_threatstop_ioc_data(api_key, url, headers):
    try:
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f'{{"error": "ThreatStop API请求失败", "details": "{str(e)}"}}')
        #sys.exit(1)


def format_output(data, ioc_value, ioc_type):
    try:
        pulse_info = data.get('pulse_info', {})
        pulses = pulse_info.get('pulses', [])
        pulse = pulses[0] if pulses else {}
        
        references = []
        for ref in pulse.get('references', []):
            if isinstance(ref, dict) and 'url' in ref:
                references.append(ref['url'])
        
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "last_modified": pulse.get('modified', ''),
            "tags": pulse.get('tags', []),
            "references": references,
            "indicator_count": data.get('indicator_count', 0),
            "validation": data.get('validation', [])
        }
    except Exception as e:
        print(f'{{"error": "数据解析失败", "details": "{str(e)}"}}')
        sys.exit(1)


def fetch_threatstop_data(api_key, base_url, ioc_type, ioc_value):
    endpoint_map = {
        'ip': 'ips',
        'domain': 'domains',
        'url': 'urls',
        'md5': 'files',
        'sha1': 'files',
        'sha256': 'files'
    }
    url = f"{base_url}{ioc_value}"
    headers = {'Authorization': f"Bearer {api_key}", 'Accept': 'application/json'}
    return fetch_threatstop_ioc_data(api_key, url, headers)

def format_threatstop_output(data, ioc_value, ioc_type):
    if not data or not isinstance(data, dict):
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "last_seen": "",
            "risk_score": -1,
            "threat_classes": [],
            "recommended_actions": [],
            "related_campaigns": []
        }
    
    try:
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "last_seen": data.get('last_seen', '') or "",
            "risk_score": int(data.get('risk_score', -1)),
            "threat_classes": list(data.get('threat_classes', [])),
            "recommended_actions": list(data.get('recommended_actions', [])),
            "related_campaigns": list(data.get('related_campaigns', []))
        }
    except Exception as e:
        print(f'{{"warning": "ThreatStop数据解析异常", "details": "{str(e)}"}}')
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "last_seen": "",
            "risk_score": -1,
            "threat_classes": [],
            "recommended_actions": [],
            "related_campaigns": []
        }

def fetch_rstcloud_data(api_key, base_url, ioc_value):
    headers = {'X-API-Key': api_key, 'Accept': 'application/json'}
    params = {'value': ioc_value}
    try:
        response = requests.get(base_url, headers=headers, params=params, timeout=15)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f'{{"warning": "RSTCloud请求异常", "details": "{str(e)}"}}')
        return None

def format_rst_output(data, ioc_value, ioc_type):
    if not data or data.get('error') == 'Not Found':
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "mal_confidence": 0,
            "threat_types": [],
            "first_seen": "",
            "last_seen": "",
            "related_ips": [],
            "reference": "",
            "tags": [],
            "threats": []
        }
    
    try:
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "mal_confidence": data.get('collect', 0),
            "threat_types": data.get('threat_types', []),
            "first_seen": data.get('fseen', ''),
            "last_seen": data.get('lseen', ''),
            "related_ips": data.get('related_ips', []),
            "reference": data.get('reference_link', ''),
            "tags": data.get('tags', []),
            "threats": data.get('threat', [])
        }
    except Exception as e:
        print(f'{{"error": "RSTCloud数据解析失败", "details": "{str(e)}"}}')
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "mal_confidence": 0,
            "threat_types": [],
            "first_seen": "",
            "last_seen": "",
            "related_ips": [],
            "reference": ""
        }

def format_whoisxml_output(data, ioc_value, ioc_type):
    try:
        result = data.get('result', {})
        return {
            "ioc": ioc_value,
            "type": ioc_type,
            "last_seen": result.get('lastSeen', ''),
            "first_seen": result.get('firstSeen', ''),
            "categories": result.get('categories', []),
            "confidence_score": result.get('confidenceScore', 0),
            "related_hashes": result.get('relatedHashes', []),
            "threat_types": result.get('threatTypes', [])
        }
    except Exception as e:
        print(f'{{"error": "WhoisXML数据解析失败", "details": "{str(e)}"}}')
        #sys.exit(1)


def get_otx_metadata(ioc_value, ioc_type, api_key, base_url):
    # 自动检测IOC类型
    output = {
        "query": {
            "ioc": ioc_value,
            "type": ioc_type,
            "res_type": "simple"
        },
        "results": {}
    }

    request_url = build_request_url(ioc_type, ioc_value, base_url)
    response_data = fetch_ioc_data(api_key, request_url)
    if response_data:
        results = {"data": response_data, "API_source": "OTX"}
        pulse_info = response_data.get('pulse_info', {})
        pulse_names = [pulse.get('name', '') for pulse in pulse_info.get('pulses', [])]
        related_data = pulse_info.get('related', {})
        alienvault_data = related_data.get('alienvault', {})
        other_data = related_data.get('other', {})
        
        filtered_data = {
            'pulse_names': pulse_names,
            'references': pulse_info.get('references', []),
            'adversary': list(set(
                alienvault_data.get('adversary', []) + 
                other_data.get('adversary', [])
            )),
            'malware_families': list(set(
                alienvault_data.get('malware_families', []) + 
                other_data.get('malware_families', [])
            ))
        }
        output["results"] = {"data": filtered_data, "API_source": "OTX"}

    return output


def main(ioc_value='google.com', sources=None, config_file='CandleTI_token_config.json'):
    if sources is None:
        sources = ['otx', 'whoisxml', 'threatstop', 'rstcloud']

    # 自动检测IOC类型
    ioc_type = detect_ioc_type(ioc_value)
    if not ioc_type:
        print(f'{"error": "无法识别的IOC类型: {ioc_value}"}')
        sys.exit(1)

    results = []
    simple_results = []
    for source in set(sources):
        try:
            if source == 'threatstop':
                continue
                api_key, base_url = load_config('ThreatStop', config_file)
                response_data = fetch_threatstop_data(api_key, base_url, ioc_type, ioc_value)
                threatstop_data = format_threatstop_output(response_data, ioc_value, ioc_type)
                if threatstop_data.get('risk_score', -1) >= 0:  # 有效性校验
                    results.append({**threatstop_data, "API_source": "ThreatStop"})
            elif source == 'rstcloud':
                continue
                api_key, base_url = load_config('RSTCloud', config_file)
                response_data = fetch_rstcloud_data(api_key, base_url, ioc_value)
                if response_data and not response_data.get('error') == 'Not Found':
                    results.append({"data": response_data, "API_source": "RSTCloud"})

                    filtered_data = {
                        "report": response_data.get("src", {}).get("report", ""),
                        "fseen": response_data.get("fseen", ""),
                        "ioc_value": response_data.get("ioc_value", ""),
                        "threat": response_data.get("threat", []),
                        "tags": response_data.get("tags", {}).get("str", []),
                        "description": response_data.get("description", "")
                    }
                    simple_results.append({"data": filtered_data, "API_source": "RSTCloud"})
            else:
                api_key, base_url = load_config('WhoisXML' if source == 'whoisxml' else 'OTX', config_file)
                
                if source == 'whoisxml':
                    continue
                    request_url = f"{base_url}?apiKey={api_key}&ioc={ioc_value}"
                    response_data = fetch_ioc_data(api_key, request_url)
                    if response_data and response_data.get('total', 0) > 0:
                        results.append({"data": response_data, "API_source": "WhoisXML"})
                        simple_results.append({"data": response_data, "API_source": "WhoisXML"})
                else:
                    request_url = build_request_url(ioc_type, ioc_value, base_url)
                    response_data = fetch_ioc_data(api_key, request_url)
                    if response_data:
                        results.append({"data": response_data, "API_source": "OTX"})
                        pulse_info = response_data.get('pulse_info', {})
                        pulse_names = [pulse.get('name', '') for pulse in pulse_info.get('pulses', [])]
                        related_data = pulse_info.get('related', {})
                        alienvault_data = related_data.get('alienvault', {})
                        other_data = related_data.get('other', {})
                        
                        filtered_data = {
                            'pulse_names': pulse_names,
                            'references': pulse_info.get('references', []),
                            'adversary': list(set(
                                alienvault_data.get('adversary', []) + 
                                other_data.get('adversary', [])
                            )),
                            'malware_families': list(set(
                                alienvault_data.get('malware_families', []) + 
                                other_data.get('malware_families', [])
                            ))
                        }
                        simple_results.append({"data": filtered_data, "API_source": "OTX"})
                
            
        except Exception as e:
            print(f'{{ "warning": "{source}查询失败", "details": "{str(e)}" }}', file=sys.stderr)
    
    output = {
        "query": {
            "ioc": ioc_value,
            "type": ioc_type,
            "res_type": "full"
        },
        "results": results
    }
    simple_output = {
        "query": {
            "ioc": ioc_value,
            "type": ioc_type,
            "res_type": "simple"
        },
        "results": simple_results
    }
    return output,simple_output


def cli():
    try:
        parser = argparse.ArgumentParser(description='IOC检查工具')
        parser.add_argument('ioc_value', nargs='?', default='app.95560.cc', help='需要检测的IOC值（默认为google.com）')
        parser.add_argument('-s', '--sources', choices=['otx', 'whoisxml', 'threatstop', 'rstcloud'], action='append', default=['otx', 'whoisxml', 'threatstop','rstcloud'],
                      help='威胁情报源（可多选）：otx/whoisxml/threatstop/rstcloud，默认')
        parser.add_argument('-c', '--config', default='CandleTI_token_config.json', help='配置文件路径（默认为CandleTI_token_config.json）')
        args = parser.parse_args()
        result, simple_res = main(ioc_value=args.ioc_value, sources=args.sources, config_file=args.config)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        print("========================")
        print(json.dumps(simple_res, indent=2, ensure_ascii=False))
    except KeyboardInterrupt:
        print('{"status": "interrupted"}')
        sys.exit(130)


if __name__ == '__main__':
    cli()

