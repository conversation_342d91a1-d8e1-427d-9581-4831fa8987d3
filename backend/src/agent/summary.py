

def generate_ioc_summary(ioc_results: list) -> str:
    """生成 IOC 分析摘要。

    Args:
        ioc_results: IOC 查询结果列表

    Returns:
        格式化的分析摘要字符串
    """
    if not ioc_results:
        return "No IOCs were analyzed."

    # summary_parts = ["## IOC Threat Intelligence Analysis\n"]
    summary_parts = []

    successful_queries = [r for r in ioc_results if r["status"] == "success"]
    failed_queries = [r for r in ioc_results if r["status"] == "error"]

    summary_parts.append(f"**Total IOCs analyzed:** {len(ioc_results)}")
    summary_parts.append(f"**Successful queries:** {len(successful_queries)}")
    summary_parts.append(f"**Failed queries:** {len(failed_queries)}\n")

    if successful_queries:
        summary_parts.append("### Threat Intelligence Results:\n")

        for result in successful_queries:
            ioc = result["ioc"]
            data = result["data"]['data']

            summary_parts.append(f"**IOC:** `{ioc}`")

            if isinstance(data, dict):
                # 尝试解析常见的威胁情报字段
                if "assertinfo" in data:
                    summary_parts.append(f"- assertinfo: {data['assertinfo']}")
                if "basicinfo" in data:
                    summary_parts.append(f"- basicinfo: {data['basicinfo']}")
                if "cip_org_info" in data:
                    summary_parts.append(f"- cip_org_info: {data['cip_org_info']}")
                if "geoinfo" in data:
                    summary_parts.append(f"- geoinfo: {data['geoinfo']}")
                if 'threatinfo' in data:
                    summary_parts.append(f"- threatinfo: {data['threatinfo']}")
            else:
                summary_parts.append(f"- Response: {str(data)[:200]}...")

            summary_parts.append("")  # 空行分隔

    if failed_queries:
        summary_parts.append("### Failed Queries:\n")
        for result in failed_queries:
            summary_parts.append(f"- **{result['ioc']}**: {result['error']}")
        summary_parts.append("")

    return "\n".join(summary_parts)


def generate_zoomeye_summary(zoomeye_results: list) -> str:
    """生成 ZoomEye 分析摘要。

    Args:
        zoomeye_results: ZoomEye 查询结果列表

    Returns:
        格式化的分析摘要字符串
    """
    if not zoomeye_results:
        return "## ZoomEye资产信息"


    # 以第一个为总体信息
    base = zoomeye_results[0]
    domain = base['domain']
    
    summary_parts = [f"## ZoomEye资产信息报告：`{domain}`\n"]
    summary_parts = []
    # 基本信息表格
    summary_parts.append("### 基本信息\n")
    summary_parts.append("| 字段 | 内容 |")
    summary_parts.append("|------|------|")
    summary_parts.append(f"| **IP 地址** | `{base['ip']}` |")
    summary_parts.append(f"| **域名** | `{base['domain']}` |")
    summary_parts.append(f"| **组织名称** | {base.get('organization_name') or '未知'} |")
    summary_parts.append(f"| **ISP** | {base.get('isp_name') or '未知'} |")
    summary_parts.append(f"| **ASN** | {base.get('asn') or '未知'} |")
    summary_parts.append(f"| **地理位置** | {base.get('country_name', '未知')}，{base.get('city_name', '未知')} |")
    summary_parts.append(f"| **坐标** | 纬度：{base.get('lat', '未知')}<br>经度：{base.get('lon', '未知')} |")
    summary_parts.append(f"| **洲/国家** | {base.get('continent_name', '未知')} / {base.get('country_name', '未知')} |\n")

    summary_parts.append("### 服务详情\n")

    for entry in zoomeye_results:
        port = entry.get('port', '未知')
        proto = entry.get("service", "unknown").upper()
        url = entry.get('url', '未知')
        summary_parts.append(f"#### `{url}`\n")
        summary_parts.append("| 项目 | 内容 |")
        summary_parts.append("|------|------|")
        summary_parts.append(f"| **端口** | {port} |")
        summary_parts.append(f"| **服务类型** | {proto} |")
        
        # 提取服务器信息
        server = ""
        if entry.get("header"):
            for line in entry.get("header", "").split("\r\n"):
                if line.lower().startswith("server:"):
                    server = line.split(":", 1)[1].strip()
        summary_parts.append(f"| **服务器信息** | `{server or '未知'}` |\n")

        # 添加返回头部信息
        summary_parts.append("**返回头部**:")
        summary_parts.append("```http")
        summary_parts.append(entry.get('header', '').strip())
        summary_parts.append("```\n")

        # 添加SSL证书信息（如果有）
        if proto == "HTTPS" and entry.get("ssl"):
            summary_parts.append("**SSL 证书信息**:")
            summary_parts.append("```")
            summary_parts.append(entry['ssl'].strip())
            summary_parts.append("```\n")

        summary_parts.append(f"**更新时间**：{entry.get('update_time', '未知')}\n")
        summary_parts.append("---\n")

    # 添加哈希摘要
    summary_parts.append("### 哈希摘要\n")
    summary_parts.append("| 项目 | 值 |")
    summary_parts.append("|------|----|")
    summary_parts.append(f"| **Header Hash** | `{base.get('header_hash', '未知')}` |")
    summary_parts.append(f"| **Body Hash** | `{base.get('body_hash', '未知')}` |")

    return "\n".join(summary_parts)


def generate_otx_summary(otx_results: dict) -> str:
    """生成 OTX 分析摘要。

    Args:
        otx_results: OTX 查询结果字典

    Returns:
        格式化的分析摘要字符串
    """
    if not otx_results or not otx_results.get('results'):
        return "## OTX威胁情报分析\n\n未找到相关威胁情报信息。"

    query_info = otx_results.get('query', {})
    results_data = otx_results.get('results', {}).get('data', {})
    
    ioc = query_info.get('ioc', '未知')
    ioc_type = query_info.get('type', '未知')
    
    summary_parts = [f"## OTX威胁情报分析报告：`{ioc}`\n"]

    # 基本信息表格
    summary_parts.append("### 基本信息\n")
    summary_parts.append("| 字段 | 内容 |")
    summary_parts.append("|------|------|")
    summary_parts.append(f"| **IOC** | `{ioc}` |")
    summary_parts.append(f"| **类型** | {ioc_type.upper()} |")
    summary_parts.append(f"| **数据源** | {otx_results.get('results', {}).get('API_source', 'OTX')} |\n")

    # 威胁脉冲信息
    pulse_names = results_data.get('pulse_names', [])
    if pulse_names:
        summary_parts.append("### 威胁记录 (Threat Pulses)\n")
        for i, pulse in enumerate(pulse_names, 1):
            summary_parts.append(f"{i}. **{pulse}**")
        summary_parts.append("")

    # 恶意软件家族
    malware_families = results_data.get('malware_families', [])
    if malware_families:
        summary_parts.append("### 恶意软件家族\n")
        summary_parts.append("| 恶意软件家族 |")
        summary_parts.append("|--------------|")
        for malware in malware_families:
            summary_parts.append(f"| `{malware}` |")
        summary_parts.append("")

    # 攻击者信息
    adversary = results_data.get('adversary', [])
    if adversary:
        summary_parts.append("### 攻击者/APT组织\n")
        summary_parts.append("| 攻击者/组织 |")
        summary_parts.append("|-------------|")
        for adv in adversary:
            summary_parts.append(f"| `{adv}` |")
        summary_parts.append("")
    else:
        summary_parts.append("### 攻击者/APT组织\n")
        summary_parts.append("暂无已知的攻击者或APT组织关联信息。\n")

    # 参考链接
    references = results_data.get('references', [])
    if references:
        summary_parts.append("### 参考链接\n")
        for i, ref in enumerate(references, 1):
            # 清理链接格式，移除多余的空格和反引号
            clean_ref = ref.strip().strip('`').strip()
            summary_parts.append(f"{i}. [{clean_ref}]({clean_ref})")
        summary_parts.append("")

    # 威胁评估总结
    summary_parts.append("### 威胁评估总结\n")
    
    threat_level = "中等"
    if malware_families or adversary:
        threat_level = "高"
    elif pulse_names:
        threat_level = "中等"
    else:
        threat_level = "低"
    
    summary_parts.append(f"**威胁等级：** {threat_level}")
    summary_parts.append(f"**记录数量：** {len(pulse_names)}")
    summary_parts.append(f"**恶意软件关联：** {len(malware_families)}")
    summary_parts.append(f"**APT组织关联：** {len(adversary)}")
    summary_parts.append(f"**参考资料：** {len(references)}")

    return "\n".join(summary_parts)
