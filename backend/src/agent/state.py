from __future__ import annotations

from dataclasses import dataclass, field
from typing import TypedDict

from langgraph.graph import add_messages
from typing_extensions import Annotated


import operator
from dataclasses import dataclass, field
from typing_extensions import Annotated


class OverallState(TypedDict):
    messages: Annotated[list, add_messages]
    search_query: Annotated[list, operator.add]
    web_research_result: Annotated[list, operator.add]
    sources_gathered: Annotated[list, operator.add]
    ioc_analysis_result: Annotated[list, operator.add]  # IOC 查询结果
    zoomeye_analysis_result: Annotated[list, operator.add]  # zoomeye查询结果
    otx_analysis_result: Annotated[list, operator.add]  # otx查询结果
    initial_search_query_count: int
    max_research_loops: int
    research_loop_count: int
    reasoning_model: str
    ioctype: str
    ioc: str
    ip: str
    releated_analysis_flag: bool
    releated_iocs: list
    end_task: bool
    dns_analysis_result: Annotated[list, operator.add]  # dns查询结果
    ip_analysis_result: Annotated[list, operator.add]  # ip查询结果
    virustotal_analysis_result: Annotated[list, operator.add]  # virustotal查询结果
    # 原始数据存储字段
    ioc_analysis_raw: dict  # IOC 原始数据
    zoomeye_analysis_raw: dict  # zoomeye 原始数据
    otx_analysis_raw: dict  # otx 原始数据
    dns_analysis_raw: dict  # dns 原始数据
    ip_analysis_raw: dict  # ip 原始数据
    virustotal_analysis_raw: dict  # virustotal 原始数据
    comprehensive_analysis_result: Annotated[list, operator.add]  # 综合分析结果
    comprehensive_analysis_raw: dict  # 综合分析原始数据


class ReflectionState(TypedDict):
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: Annotated[list, operator.add]
    research_loop_count: int
    number_of_ran_queries: int


class Query(TypedDict):
    query: str
    rationale: str


class QueryGenerationState(TypedDict):
    query_list: list[Query]


class WebSearchState(TypedDict):
    search_query: str
    id: str


@dataclass(kw_only=True)
class SearchStateOutput:
    running_summary: str = field(default=None)  # Final report
