import os
import json
import re
import requests
import os
import time
import socket
from urllib.parse import urlparse

# Removed unused imports: SearchQueryList, Reflection (using JSON parsing instead)
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
# from google.genai import Client  # Removed for DeepSeek migration

# Import logging system
from agent.logger import get_logger, node_enter, node_exit, set_log_level

from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
)
from agent.configuration import Configuration
from api.zeye import ZoomEyeV2
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    query_writer_normal_instructions,
    web_searcher_instructions,
    reflection_instructions,
    ioc_search_instructions,
    answer_instructions,
)

from agent.summary import (
    generate_ioc_summary,
    generate_zoomeye_summary,
    generate_otx_summary,
)

from api.CandleTI_ioc_check_otx_whoisxml_threatstop import get_otx_metadata

from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from agent.utils import (
    get_research_topic,
)

load_dotenv()

def get_llm_model(model_name: str, temperature: float = 0.1):
    """
    Helper function to get the appropriate LLM model based on provider and model name.
    
    Args:
        model_name: The name of the model to use
        temperature: The temperature to use for generation
        
    Returns:
        An instance of ChatOpenAI or ChatGoogleGenerativeAI
    """
    logger = get_logger()
    logger.model_config(model_name, temperature=temperature)
    if model_name == 'gemini-2.5-flash':
        os.environ['HTTP_PROXY'] = "socks5h://172.17.0.1:15231"
        os.environ['HTTPS_PROXY'] = "socks5h://172.17.0.1:15231"
    api_key = os.getenv("GEMINI_API_KEY") if model_name == 'gemini-2.5-flash' else os.getenv("DEEPSEEK_API_KEY")
    base_url = "https://generativelanguage.googleapis.com/v1beta" if model_name == 'gemini-2.5-flash' else "https://api.siliconflow.cn/v1"
    if api_key is None:
        logger.error("API key not found", model=model_name)
        raise ValueError("GEMINI_API_KEY or DEEPSEEK_API_KEY is not set.")
    
    return ChatOpenAI(
        model=model_name,
        temperature=temperature,
        max_retries=2,
        api_key=api_key,
        base_url=base_url,
    )

# Note: Google Search API functionality removed for DeepSeek migration
# Using SiliconFlow API endpoint for DeepSeek models

# Nodes

def zoomeye_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    """Search IOC information from ZoomEye"""
    logger = get_logger()
    node_enter("zoomeye_analysis", {"ioc": state.get('ioc'), "ioctype": state.get('ioctype')})
    
    try:
        if 'domain' == state['ioctype']:
            query = 'domain="%s" ' % state['ioc']
        else:
            query = 'ip="%s"' % (state['ioc'])
        
        logger.analysis_start("ZoomEye", state['ioc'])
        logger.debug("ZoomEye query constructed", query=query)
        
        zoomeye_handle = ZoomEyeV2('25B49348-8Ca2-5e864-9045-b9ef9d7c690')
        _, result = zoomeye_handle.search(query)
        result_list = []
        for ret in result:
            result_list.append(ret)

        analysis_summary = generate_zoomeye_summary(result_list)
        logger.analysis_complete("ZoomEye", state['ioc'], len(result_list))
        
        result = {'zoomeye_analysis_result': [analysis_summary]}
        node_exit("zoomeye_analysis", {"result_count": len(result_list)})
        return result
        
    except Exception as e:
        logger.analysis_error("ZoomEye", state.get('ioc', 'unknown'), str(e))
        node_exit("zoomeye_analysis", {"error": str(e)})
        return {'zoomeye_analysis_result': [f"ZoomEye analysis failed: {str(e)}"]}

def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates a search queries based on the User's question.

    Uses DeepSeek Chat to create an optimized search query for web research based on
    the User's question.

    Args:
        state: Current graph state containing the User's question
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated query
    """
    logger = get_logger()
    node_enter("generate_query", {"messages_count": len(state.get("messages", []))})
    
    configurable = Configuration.from_runnable_config(config)

    # check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries

    # init LLM using get_llm_model helper
    model_name = state.get("reasoning_model", configurable.query_generator_model)
    try:
        llm = get_llm_model(
            model_name=model_name,
            temperature=1.0
        )
    except Exception as e:
        logger.error("Failed to initialize LLM", error=str(e))
        node_exit("generate_query", {"error": str(e)})
        raise e

    message = get_research_topic(state["messages"])
    research_format = f"""You are a senior network threat intelligence analysis expert. Please conduct a detailed network security profile analysis of the following network assets, focusing solely on network security perspectives. Target network asset: {message}"""
    # Format the prompt with JSON output instructions
    current_date = get_current_date()
    if model_name == 'gemini-2.5-flash':
        formatted_prompt = query_writer_instructions.format(
            current_date=current_date,
            research_topic=research_format,
            number_queries=state["initial_search_query_count"],
        )
    else:
        formatted_prompt = query_writer_normal_instructions.format(
            current_date=current_date,
            research_topic=research_format,
            number_queries=state["initial_search_query_count"],
        )

    # Add JSON format instructions to the prompt
    json_prompt = f"""{formatted_prompt}

Please respond with a valid JSON object in the following format:
{{
    "query": ["query1", "query2", "query3"],
    "rationale": "explanation of why these queries are relevant"
}}

Ensure your response is valid JSON only, without any additional text or formatting."""

    # Generate the search queries
    response = llm.invoke(json_prompt)

    # Parse JSON response
    try:
        cleaned = re.sub(r"^```json|```$", "", response.content.strip(), flags=re.MULTILINE).strip()
        result_data = json.loads(cleaned)
        # result_data = json.loads(response.content)
        result = {"query_list": result_data["query"]}
        logger.debug("Query generation successful", query_count=len(result["query_list"]))
        node_exit("generate_query", {"query_count": len(result["query_list"])})
        return result
    except (json.JSONDecodeError, KeyError) as e:
        # Fallback: extract queries from text response
        logger.warning("Failed to parse JSON response, using fallback", error=str(e))
        # Simple fallback - generate basic queries
        topic = get_research_topic(state["messages"])
        fallback_queries = [f"{topic}", f"{topic} overview", f"{topic} details"]
        logger.debug("Using fallback queries", queries=fallback_queries)
        result = {"query_list": fallback_queries[:state["initial_search_query_count"]]}
        node_exit("generate_query", {"query_count": len(result["query_list"]), "fallback": True})
        return result


def continue_to_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the web research node.

    This is used to spawn n number of web research nodes, one for each search query.
    """
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["query_list"][:3])
    ]


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using DeepSeek's knowledge base.

    Since Google Search API is not available with DeepSeek, this function uses the LLM's
    knowledge base to generate research responses based on the search query.

    Args:
        state: Current graph state containing the search query and research loop count
        config: Configuration for the runnable, including LLM settings

    Returns:
        Dictionary with state update, including sources_gathered, research_loop_count, and web_research_results
    """
    logger = get_logger()
    node_enter("web_research", {"search_query": state['search_query'], "id": state.get('id')})
    
    # Configure
    configurable = Configuration.from_runnable_config(config)
    logger.analysis_start("Web Research", state['search_query'])
    # Note: Google Search functionality removed for DeepSeek migration
    # For now, we'll use the LLM to generate a research response based on the query
    llm = get_llm_model(
        model_name=state.get("reasoning_model", configurable.query_generator_model),
        temperature=0
    )
    formatted_prompt = web_searcher_instructions.format(
        current_date=get_current_date(),
        research_topic=state["search_query"],
    )
    time.sleep(5)
    response = llm.invoke(formatted_prompt)
    modified_text = response.content

    # Create mock sources since we don't have real web search
    sources_gathered = [{
        "label": "DeepSeek Knowledge Base",
        "short_url": "https://deepseek.com/knowledge",
        "value": "https://deepseek.com/knowledge"
    }]

    result = {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text],
    }
    node_exit("web_research", result)
    return result


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """
    LangGraph node that identifies knowledge gaps and generates potential follow-up queries.

    Analyzes the current summary to identify areas for further research and generates
    potential follow-up queries. Uses structured output to extract
    the follow-up query in JSON format.

    Args:
        state: Current graph state containing the running summary and research topic
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated follow-up query
    """
    logger = get_logger()
    node_enter("reflection", {"research_loop_count": state.get("research_loop_count", 0)})
    
    configurable = Configuration.from_runnable_config(config)
    # Increment the research loop count and get the reasoning model
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model", configurable.reflection_model)

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = reflection_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )
    # init Reasoning Model using get_llm_model helper
    llm = get_llm_model(
        model_name=reasoning_model,
        temperature=1.0
    )

    # Add JSON format instructions to the prompt
    json_prompt = f"""{formatted_prompt}

Please respond with a valid JSON object in the following format:
{{
    "is_sufficient": true/false,
    "knowledge_gap": "description of what information is missing",
    "follow_up_queries": ["query1", "query2", "query3"]
}}

Ensure your response is valid JSON only, without any additional text or formatting."""

    response = llm.invoke(json_prompt)

    # Parse JSON response
    try:
        cleaned = re.sub(r"^```json|```$", "", response.content.strip(), flags=re.MULTILINE).strip()
        result_data = json.loads(cleaned)
        # result_data = json.loads(response.content)
        result = {
            "is_sufficient": result_data["is_sufficient"],
            "knowledge_gap": result_data["knowledge_gap"],
            "follow_up_queries": result_data["follow_up_queries"],
            "research_loop_count": state["research_loop_count"],
            "number_of_ran_queries": len(state["search_query"]),
        }
        logger.debug(f"Reflection result: {json.dumps(result, ensure_ascii=False, indent=2)}")
        node_exit("reflection", result)
        return result
    except (json.JSONDecodeError, KeyError) as e:
        # Fallback: assume more research is needed
        logger.error(f"Failed to parse JSON response in reflection: {e}")
        fallback_result = {
            "is_sufficient": False,
            "knowledge_gap": "Unable to parse reflection response",
            "follow_up_queries": ["additional research needed"],
            "research_loop_count": state["research_loop_count"],
            "number_of_ran_queries": len(state["search_query"]),
        }
        node_exit("reflection", fallback_result)
        return fallback_result
def evaluate_research(
    state: ReflectionState,
    config: RunnableConfig,
) -> OverallState:
    """LangGraph routing function that determines the next step in the research flow.

    Controls the research loop by deciding whether to continue gathering information
    or to finalize the summary based on the configured maximum number of research loops.

    Args:
        state: Current graph state containing the research loop count
        config: Configuration for the runnable, including max_research_loops setting

    Returns:
        String literal indicating the next node to visit ("web_research" or "finalize_summary")
    """
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_answer"
    else:
        return [
            Send(
                "web_research",
                {
                    "search_query": follow_up_query,
                    "id": state["number_of_ran_queries"] + int(idx),
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]


def extract_iocs(text: str) -> dict:
    """Extract IOCs (IP, URL, domain) from text.

    Args:
        config: Configuration information
        text: Text content to analyze

    Returns:
        Dictionary containing extracted IOCs, includes end_task=True flag if no IOCs are parsed
    """
    logger = get_logger()
    node_enter("extract_iocs", {"text_length": len(text)})
    
    try:
        llm = get_llm_model(
            model_name='deepseek-ai/DeepSeek-V3',
            temperature=1.0
        )

        formatted_prompt = ioc_search_instructions.format(
            text=text,
        )

        json_prompt = f"""{formatted_prompt}

Please respond with a valid JSON object in the following format:
{{
    "ips": [],
    "domains": [],
    "urls": []
}}

Ensure your response is valid JSON only, without any additional text or formatting."""

        response = llm.invoke(json_prompt)
        cleaned = re.sub(r"^```json|```$", "", response.content.strip(), flags=re.MULTILINE).strip()
        result_data = json.loads(cleaned)
        logger.debug(f"Parsed IOCs: {result_data}")
        
        # Check if IOCs were parsed
        total_iocs = len(result_data.get("ips", [])) + len(result_data.get("urls", [])) + len(result_data.get("domains", []))
        if total_iocs == 0:
            result_data["end_task"] = True
            logger.info("No IOCs found in text, marking task as ended")
        else:
            logger.info(f"Successfully extracted {total_iocs} IOCs from text")
            
        node_exit("extract_iocs", result_data)
        return result_data
    except Exception as e:
        logger.error(f"Failed to parse IOCs: {str(e)}")
        error_result = {
            "ips": [],
            "urls": [],
            "domains": [],
            "end_task": True  # Mark task as ended if parsing fails
        }
        node_exit("extract_iocs", error_result)
        return error_result

def query_ioc_api(ioc: str) -> dict:
    """Query third-party IOC API to get threat intelligence.

    Args:
        ioc: IOC to query (IP, URL or domain)

    Returns:
        Dictionary containing query results
    """
    try:
        api_url = f'http://**************:40381/ZIwTuwmXcBWjYhyCdTlo/search/?searchCon={ioc}'

        # Set timeout and retry
        response = requests.get(api_url, timeout=60)

        if response.status_code == 200:
            try:
                result = response.json()
                return {
                    "ioc": ioc,
                    "status": "success",
                    "data": result
                }
            except json.JSONDecodeError:
                return {
                    "ioc": ioc,
                    "status": "success",
                    "data": {"raw_response": response.text}
                }
        else:
            return {
                "ioc": ioc,
                "status": "error",
                "error": f"HTTP {response.status_code}: {response.text}"
            }

    except requests.exceptions.Timeout:
        return {
            "ioc": ioc,
            "status": "error",
            "error": "Request timeout"
        }
    except requests.exceptions.RequestException as e:
        return {
            "ioc": ioc,
            "status": "error",
            "error": f"Request failed: {str(e)}"
        }
    except Exception as e:
        return {
            "ioc": ioc,
            "status": "error",
            "error": f"Unexpected error: {str(e)}"
        }

def check_is_hash(s: str) -> bool:
    s = s.lower()
    if re.fullmatch(r"[a-f0-9]{32}", s):
        return True
    elif re.fullmatch(r"[a-f0-9]{40}", s):
        return True
    elif re.fullmatch(r"[a-f0-9]{64}", s):
        return True
    elif re.fullmatch(r"[a-f0-9]{128}", s):
        return True
    elif re.fullmatch(r"[a-f0-9]{8}", s):
        return True
    elif re.fullmatch(r"[a-zA-Z0-9+/=]{16,}", s):
        return True
    else:
        return False

def ioc_analysis(state: OverallState, releated_analysis_flag: bool) -> OverallState:
    """LangGraph node for analyzing IOCs in search results and querying threat intelligence.

    Extract IOCs such as IP, URL, domain from web_research_result,
    then query third-party APIs to get threat intelligence information.

    Args:
        state: Current graph state containing search results
        config: Runtime configuration

    Returns:
        State update containing IOC analysis results
    """
    logger = get_logger()
    node_enter("ioc_analysis", {"releated_analysis_flag": releated_analysis_flag, "releated_iocs_count": len(state.get('releated_iocs', []))})

    # If there are related IOCs, perform related IOC query
    if len(state.get('releated_iocs', [])) != 0:
        content = state.get('ioc')
        logger.analysis_start("IOC Analysis", f"Analyzing related IOC: {content}")
    else:
        # Merge all search result texts
        content = get_research_topic(state["messages"])
        logger.analysis_start("IOC Analysis", f"Analyzing research topic: {content}")

    if not content.strip():
        logger.warning("No web research results to analyze")
        error_result = {"ioc_analysis_result": [], "end_task": True}
        node_exit("ioc_analysis", error_result)
        return error_result

    # Extract IOCs
    iocs = extract_iocs(content)
    
    # Check if task needs to be ended
    if iocs.get("end_task", False):
        logger.warning("No IOCs found or extraction failed, ending task")
        error_result = {"ioc_analysis_result": ["No IOCs found in the research results."], "end_task": True}
        node_exit("ioc_analysis", error_result)
        return error_result

    total_iocs = len(iocs["ips"]) + len(iocs["urls"]) + len(iocs["domains"])
    logger.info(f"Found {total_iocs} IOCs in content: {len(iocs['ips'])} IPs, {len(iocs['urls'])} URLs, {len(iocs['domains'])} domains")
    logger.debug(f"Content analyzed: {content[:100]}...")

    if total_iocs == 0:
        error_result = {"ioc_analysis_result": ["No IOCs found in the research results."], "end_task": True}
        node_exit("ioc_analysis", error_result)
        return error_result

    # Query each IOC
    ioc_results = []
    all_iocs = iocs["ips"] + iocs["urls"] + iocs["domains"]
    threat_level = ''
    # Limit query count to avoid too many API calls
    max_iocs = 10
    if len(all_iocs) > max_iocs:
        logger.warning(f"Too many IOCs found ({len(all_iocs)}), limiting to first {max_iocs}")
        all_iocs = all_iocs[:max_iocs]
    releated_iocs = []
    for ioc in all_iocs[:1]:
        logger.info(f"Querying IOC: {ioc}")
        result = query_ioc_api(ioc)
        if 'data' in result and 'data' in result['data'] and 'basicinfo' in result['data']['data'] and result['data']['data']['basicinfo']:
            if releated_analysis_flag == False:   # Only record related IOCs for the first time
                org_releated_iocs = result['data']['data']['basicinfo'][0]['releated_iocs']
                for ioc in org_releated_iocs:
                    if not check_is_hash(ioc) and ioc not in releated_iocs:
                        releated_iocs.append(ioc)
                logger.debug(f"Found related IOCs: {releated_iocs}")
            threat_level = result['data']['data']['basicinfo'][0]['threatlevel']
       
        ioc_results.append(result)
        break

    # Generate analysis summary
    analysis_summary = generate_ioc_summary(ioc_results)
    for k, v in iocs.items():
        if v:
            ioctype = k[:-1]
            ioc = v[0]
            break

    final_result = {"ioc_analysis_result": [analysis_summary], 'ioc': ioc, 'ioctype': ioctype, 'end_task': False, 'releated_iocs': releated_iocs, 'threat_level': threat_level}
    logger.analysis_complete("IOC Analysis", f"Completed analysis for IOC: {ioc}")
    node_exit("ioc_analysis", final_result)
    return final_result


def finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the research summary.

    Prepares the final output by deduplicating and formatting sources, then
    combining them with the running summary to create a well-structured
    research report with proper citations.

    Args:
        state: Current graph state containing the running summary and sources gathered

    Returns:
        Dictionary with state update, including running_summary key containing the formatted final summary with sources
    """
    logger = get_logger()
    node_enter("finalize_answer", {"sources_count": len(state.get("sources_gathered", []))})
    
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model", configurable.answer_model)
    logger.model_config(f"Using reasoning model: {reasoning_model}")

    # Format the prompt, including IOC analysis if available
    current_date = get_current_date()

    # Merge research results and IOC analysis results
    summaries = "\n---\n\n".join(state["web_research_result"])

    # Add IOC analysis results (if available)
    comprehensive_analysis_results = state.get("comprehensive_analysis_result", [])
    if comprehensive_analysis_results:
        summaries += "\n\n".join(comprehensive_analysis_results)

    formatted_prompt = answer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries=summaries,
    )
    logger.debug(f"Formatted prompt for final answer: {formatted_prompt[:200]}...")
    # init Reasoning Model using get_llm_model helper
    llm = get_llm_model(
        model_name=reasoning_model,
        temperature=0
    )
    result = llm.invoke(formatted_prompt)

    # Replace the short urls with the original urls and add all used urls to the sources_gathered
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            result.content = result.content.replace(
                source["short_url"], source["value"]
            )
            unique_sources.append(source)
            

    final_result = {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources,
    }
    node_exit("finalize_answer", {})
    return final_result


def otx_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    logger = get_logger()
    ioc_value = state['ioc']
    ioctype = state['ioctype']
    node_enter("otx_analysis", {"ioc": ioc_value, "ioctype": ioctype})
    
    logger.analysis_start("OTX Analysis", f"Analyzing {ioctype}: {ioc_value}")
    result = get_otx_metadata(ioc_value, ioctype, api_key="c448a85dff857bad48573a2e60f3e670d37b1d29d09cf44fb97c77bb6370399b", base_url="https://otx.alienvault.com/api/v1/indicators/")
    state['otx_analysis_result'] = result

    analysis_summary = generate_otx_summary(result)
    final_result = {'otx_analysis_result': [analysis_summary]}
    logger.analysis_complete("OTX Analysis", f"Completed OTX analysis for {ioctype}: {ioc_value}")
    logger.debug(f"OTX analysis result: {json.dumps(result, ensure_ascii=False, indent=2)}")
    node_exit("otx_analysis", final_result)
    return final_result


def dns_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    """DNS information query analysis"""
    import dns.resolver
    
    logger = get_logger()
    target = state.get('ioc', '')
    ioctype = state.get('ioctype', '')
    node_enter("dns_analysis", {"target": target, "ioctype": ioctype})
    
    # If not domain type, try to extract domain from URL
    if ioctype != 'domain':
        if ioctype == 'url':
            try:
                parsed = urlparse(target)
                target = parsed.netloc
                if target:
                    ioctype = 'domain'
            except:
                pass
    
    result = {
        "source": "DNS Analysis",
        "target": target,
        "type": ioctype,
        "status": "error",
        "data": {},
        "error": None,
        "raw_data": {}
    }
    
    if ioctype != 'domain' or not target:
        result["error"] = "Target is not a valid domain"
        logger.warning(f"Invalid domain target: {target} (type: {ioctype})")
        error_result = {'dns_analysis_result': [json.dumps(result, ensure_ascii=False)]}
        node_exit("dns_analysis", error_result)
        return error_result
    
    try:
        dns_info = {}
        record_types = ["A", "AAAA", "MX", "NS", "TXT", "CNAME", "SOA"]
        
        for record_type in record_types:
            try:
                answers = dns.resolver.resolve(target, record_type)
                dns_info[record_type] = [str(r) for r in answers]
            except Exception as e:
                dns_info[record_type] = []
                logger.debug(f"DNS query {target} {record_type} record failed: {str(e)}")
        
        # Build analysis results
        result["status"] = "success"
        result["data"] = {
            "domain": target,
            "dns_records": dns_info,
            "record_count": sum(len(records) for records in dns_info.values()),
            "available_records": [rt for rt, records in dns_info.items() if records]
        }
        result["raw_data"] = dns_info
        
        # Generate formatted analysis summary
        summary_lines = [f"## DNS Information Query Results\n"]
        summary_lines.append(f"**Query Domain:** `{target}`\n")
        
        if result["data"]["record_count"] == 0:
            summary_lines.append(f"No DNS information found for domain `{target}`.")
        else:
            for record_type, records in dns_info.items():
                summary_lines.append(f"### {record_type} Records")
                if records:
                    for record in records:
                        summary_lines.append(f"- {record}")
                    summary_lines.append("")
                else:
                    summary_lines.append("None\n")
        
        analysis_summary = "\n".join(summary_lines)
        
    except Exception as e:
        result["error"] = f"DNS query exception: {str(e)}"
        analysis_summary = f"DNS query failed: {str(e)}"
        logger.error(f"DNS analysis failed for {target}: {str(e)}")
    
    final_result = {'dns_analysis_result': [analysis_summary], 'dns_analysis_raw': result}
    logger.analysis_complete("DNS Analysis", f"Completed DNS analysis for domain: {target}")
    logger.debug(f"DNS analysis result: {json.dumps(result, ensure_ascii=False, indent=2)}")
    node_exit("dns_analysis", final_result)
    return final_result


def ip_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    """IP threat intelligence comprehensive analysis"""
    import ipaddress
    import dns.resolver
    import dns.reversename
    from urllib.request import urlopen, Request
    from urllib.error import URLError, HTTPError
    from urllib.parse import urlencode
    
    logger = get_logger()
    target = state.get('ip', '') or state.get('ioc', '')
    ioctype = state.get('ioctype', '')
    node_enter("ip_analysis", {"target": target, "ioctype": ioctype})
    
    # If it's a domain, resolve to IP first
    if ioctype == 'domain':
        try:
            import dns.resolver
            answers = dns.resolver.resolve(target, 'A')
            if answers:
                target = str(answers[0])
                ioctype = 'ip'
        except:
            pass
    elif ioctype == 'url':
        try:
            parsed = urlparse(target)
            hostname = parsed.netloc
            if hostname:
                answers = dns.resolver.resolve(hostname, 'A')
                if answers:
                    target = str(answers[0])
                    ioctype = 'ip'
        except:
            pass
    
    # Validate IP address format
    def validate_ip(ip_address: str) -> bool:
        try:
            ipaddress.ip_address(ip_address)
            return True
        except ValueError:
            return False
    
    result = {
        "source": "IP Analysis",
        "target": target,
        "type": ioctype,
        "status": "error",
        "data": {},
        "error": None,
        "raw_data": {}
    }
    
    if not validate_ip(target):
        result["error"] = "Target is not a valid IP address"
        logger.warning(f"Invalid IP address target: {target}")
        error_result = {'ip_analysis_result': [json.dumps(result, ensure_ascii=False)]}
        node_exit("ip_analysis", error_result)
        return error_result
    
    try:
        # HTTP request function
        def make_http_request(url: str, headers=None) -> dict:
            try:
                req = Request(url, headers=headers or {})
                with urlopen(req, timeout=10) as response:
                    data = response.read().decode('utf-8')
                    return json.loads(data)
            except Exception as e:
                logger.debug(f"HTTP request failed: {e}")
                return None
        
        analysis_results = {}
        
        # 1. ipinfo.io query
        try:
            url = f"https://ipinfo.io/{target}/json"
            data = make_http_request(url)
            if data:
                analysis_results['ipinfo'] = {
                    "status": "success",
                    "data": {
                        "ip": data.get("ip", target),
                        "city": data.get("city", "Unknown"),
                        "region": data.get("region", "Unknown"),
                        "country": data.get("country", "Unknown"),
                        "location": data.get("loc", "Unknown"),
                        "organization": data.get("org", "Unknown"),
                        "postal": data.get("postal", "Unknown"),
                        "timezone": data.get("timezone", "Unknown")
                    }
                }
        except Exception as e:
            analysis_results['ipinfo'] = {"status": "error", "error": str(e)}
        
        # 2. DNS PTR query
        try:
            ptr_records = []
            try:
                reversed_dns = dns.reversename.from_address(target)
                answers = dns.resolver.resolve(reversed_dns, "PTR")
                ptr_records = [str(rdata) for rdata in answers]
            except:
                ptr_records = []
            
            analysis_results['dns_ptr'] = {
                "status": "success",
                "data": {
                    "ip": target,
                    "ptr_records": ptr_records,
                    "ptr_count": len(ptr_records)
                }
            }
        except Exception as e:
            analysis_results['dns_ptr'] = {"status": "error", "error": str(e)}
        
        # 3. AbuseIPDB query (using default API key)
        try:
            abuseipdb_api_key = "********************************************************************************"
            params = {
                'ipAddress': target,
                'maxAgeInDays': 90,
                'verbose': ''
            }
            url = f"https://api.abuseipdb.com/api/v2/check?{urlencode(params)}"
            headers = {
                'Key': abuseipdb_api_key,
                'Accept': 'application/json'
            }
            data = make_http_request(url, headers)
            if data and 'data' in data:
                abuse_data = data['data']
                analysis_results['abuseipdb'] = {
                    "status": "success",
                    "data": {
                        "ip": abuse_data.get("ipAddress", target),
                        "abuse_confidence": abuse_data.get("abuseConfidencePercentage", 0),
                        "country_code": abuse_data.get("countryCode", "Unknown"),
                        "usage_type": abuse_data.get("usageType", "Unknown"),
                        "isp": abuse_data.get("isp", "Unknown"),
                        "total_reports": abuse_data.get("totalReports", 0),
                        "last_reported_at": abuse_data.get("lastReportedAt", "Never reported")
                    }
                }
        except Exception as e:
            analysis_results['abuseipdb'] = {"status": "error", "error": str(e)}
        
        # Build comprehensive analysis results
        result["status"] = "success"
        result["data"] = analysis_results
        result["raw_data"] = analysis_results
        
        # Generate threat assessment
        threat_level = "🟢 Low Risk"
        if 'abuseipdb' in analysis_results and analysis_results['abuseipdb'].get('status') == 'success':
            confidence = analysis_results['abuseipdb']['data'].get('abuse_confidence', 0)
            if confidence >= 75:
                threat_level = "🔴 High Risk"
            elif confidence >= 25:
                threat_level = "🟡 Medium Risk"
        
        # Generate formatted analysis summary
        summary_lines = [f"## IP Threat Intelligence Comprehensive Analysis Results\n"]
        summary_lines.append(f"**Target IP Address:** `{target}`")
        summary_lines.append(f"**Threat Level:** {threat_level}\n")
        
        # ipinfo.io results
        if 'ipinfo' in analysis_results and analysis_results['ipinfo'].get('status') == 'success':
            ipinfo_data = analysis_results['ipinfo']['data']
            summary_lines.append(f"### Geographic Information (ipinfo.io)")
            summary_lines.append(f"- **Country/Region:** {ipinfo_data.get('country', 'Unknown')}")
            summary_lines.append(f"- **City:** {ipinfo_data.get('city', 'Unknown')}")
            summary_lines.append(f"- **Organization:** {ipinfo_data.get('organization', 'Unknown')}")
            summary_lines.append(f"- **Timezone:** {ipinfo_data.get('timezone', 'Unknown')}\n")
        
        # AbuseIPDB results
        if 'abuseipdb' in analysis_results and analysis_results['abuseipdb'].get('status') == 'success':
            abuse_data = analysis_results['abuseipdb']['data']
            summary_lines.append(f"### Malicious Score (AbuseIPDB)")
            summary_lines.append(f"- **Abuse Confidence:** {abuse_data.get('abuse_confidence', 0)}%")
            summary_lines.append(f"- **Total Reports:** {abuse_data.get('total_reports', 0)}")
            summary_lines.append(f"- **ISP:** {abuse_data.get('isp', 'Unknown')}")
            summary_lines.append(f"- **Last Reported:** {abuse_data.get('last_reported_at', 'Never reported')}\n")
        
        # DNS PTR results
        if 'dns_ptr' in analysis_results and analysis_results['dns_ptr'].get('status') == 'success':
            ptr_data = analysis_results['dns_ptr']['data']
            summary_lines.append(f"### DNS Reverse Resolution (PTR)")
            if ptr_data.get('ptr_records'):
                for ptr in ptr_data['ptr_records']:
                    summary_lines.append(f"- {ptr}")
            else:
                summary_lines.append(f"- No PTR records")
            summary_lines.append("")
        
        analysis_summary = "\n".join(summary_lines)
        
    except Exception as e:
        result["error"] = f"IP analysis exception: {str(e)}"
        analysis_summary = f"IP analysis failed: {str(e)}"
        logger.error(f"IP analysis failed for {target}: {str(e)}")
    
    final_result = {'ip_analysis_result': [analysis_summary], 'ip_analysis_raw': result}
    logger.analysis_complete("IP Analysis", f"Completed IP analysis for: {target}")
    logger.debug(f"IP analysis result: {json.dumps(result, ensure_ascii=False, indent=2)}")
    node_exit("ip_analysis", final_result)
    return final_result


def virustotal_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    """VirusTotal threat intelligence analysis"""
    import base64
    import hashlib
    from urllib.request import urlopen, Request
    from urllib.error import URLError, HTTPError
    from urllib.parse import urlencode, urlparse
    
    logger = get_logger()
    target = state.get('ip', '') or state.get('ioc', '')
    ioctype = state.get('ioctype', '')
    node_enter("virustotal_analysis", {"target": target, "ioctype": ioctype})
    
    # VirusTotal API key
    api_key = "****************************************************************"
    
    result = {
        "source": "VirusTotal Analysis",
        "target": target,
        "type": ioctype,
        "status": "error",
        "data": {},
        "error": None,
        "raw_data": {}
    }
    
    def make_vt_request(url: str, headers=None) -> dict:
        """Send VirusTotal API request"""
        try:
            logger.debug(f"VirusTotal API {url} request started")
            req = Request(url, headers=headers or {})
            with urlopen(req, timeout=30) as response:
                data = response.read().decode('utf-8')
                return json.loads(data)
        except Exception as e:
            logger.debug(f"VirusTotal API {url} request failed: {e}")
            return None
    
    def get_relations(target_id: str, target_type: str) -> dict:
        """Get relationship information"""
        relations = {}
        relation_types = {
            'ip_addresse': ['communicating_files', 'downloaded_files', 'referrer_files', 'urls', 'resolutions'],
            'domain': ['communicating_files', 'downloaded_files', 'referrer_files', 'urls', 'subdomains', 'resolutions'],
            'url': ['communicating_files', 'downloaded_files', 'referrer_files']
        }
        
        for relation_type in relation_types.get(target_type, []):
            try:
                target_type = 'ip_addresse' if target_type == 'ip' else target_type
                url = f"https://www.virustotal.com/api/v3/{target_type}s/{target_id}/{relation_type}?limit=10"
                headers = {"X-Apikey": api_key, "Accept": "application/json"}
                data = make_vt_request(url, headers)
                if data and 'data' in data:
                    relations[relation_type] = data['data'][:10]  # Limit quantity
            except:
                relations[relation_type] = []
        
        return relations
    
    try:
        headers = {"X-Apikey": api_key, "Accept": "application/json"}
        analysis_data = {}
        
        if ioctype == 'ip':
            # IP address query
            url = f"https://www.virustotal.com/api/v3/ip_addresses/{target}"
            data = make_vt_request(url, headers)
            
            if data and 'data' in data:
                attributes = data['data'].get('attributes', {})
                analysis_data = {
                    "ip": target,
                    "country": attributes.get('country', 'Unknown'),
                    "asn": attributes.get('asn', 'Unknown'),
                    "as_owner": attributes.get('as_owner', 'Unknown'),
                    "reputation": attributes.get('reputation', 0),
                    "last_analysis_stats": attributes.get('last_analysis_stats', {}),
                    "last_analysis_date": attributes.get('last_analysis_date', 0),
                    "whois": attributes.get('whois', ''),
                    "relations": get_relations(target, 'ip')
                }
        
        elif ioctype == 'domain':
            # Domain query
            url = f"https://www.virustotal.com/api/v3/domains/{target}"
            data = make_vt_request(url, headers)
            
            if data and 'data' in data:
                attributes = data['data'].get('attributes', {})
                analysis_data = {
                    "domain": target,
                    "registrar": attributes.get('registrar', 'Unknown'),
                    "creation_date": attributes.get('creation_date', 0),
                    "last_update_date": attributes.get('last_update_date', 0),
                    "expiration_date": attributes.get('expiration_date', 0),
                    "reputation": attributes.get('reputation', 0),
                    "last_analysis_stats": attributes.get('last_analysis_stats', {}),
                    "last_analysis_date": attributes.get('last_analysis_date', 0),
                    "categories": attributes.get('categories', {}),
                    "whois": attributes.get('whois', ''),
                    "dns_records": attributes.get('last_dns_records', []),
                    "relations": get_relations(target, 'domain')
                }
        
        elif ioctype == 'url':
            # URL query
            url_id = base64.urlsafe_b64encode(target.encode()).decode().strip('=')
            url = f"https://www.virustotal.com/api/v3/urls/{url_id}"
            data = make_vt_request(url, headers)
            
            if not data or 'data' not in data:
                # If URL doesn't exist, submit for analysis
                submit_url = "https://www.virustotal.com/api/v3/urls"
                submit_data = urlencode({'url': target}).encode()
                req = Request(submit_url, data=submit_data, headers=headers)
                req.add_header('Content-Type', 'application/x-www-form-urlencoded')
                
                with urlopen(req, timeout=30) as response:
                    submit_result = json.loads(response.read().decode('utf-8'))
                    analysis_id = submit_result.get('data', {}).get('id', '')
                    
                    if analysis_id:
                        # Wait for analysis completion
                        import time
                        time.sleep(10)
                        
                        analysis_url = f"https://www.virustotal.com/api/v3/analyses/{analysis_id}"
                        data = make_vt_request(analysis_url, headers)
            
            if data and 'data' in data:
                attributes = data['data'].get('attributes', {})
                analysis_data = {
                    "url": target,
                    "final_url": attributes.get('final_url', target),
                    "title": attributes.get('title', 'Unknown'),
                    "last_analysis_date": attributes.get('last_analysis_date', 0),
                    "times_submitted": attributes.get('times_submitted', 0),
                    "last_analysis_stats": attributes.get('last_analysis_stats', {}),
                    "threat_names": attributes.get('threat_names', []),
                    "categories": attributes.get('categories', {}),
                    "reputation": attributes.get('reputation', 0),
                    "relations": get_relations(url_id, 'url')
                }
        
        if analysis_data:
            result["status"] = "success"
            result["data"] = analysis_data
            result["raw_data"] = analysis_data
            
            # Generate threat assessment
            stats = analysis_data.get('last_analysis_stats', {})
            malicious = stats.get('malicious', 0)
            suspicious = stats.get('suspicious', 0)
            total = sum(stats.values()) if stats else 0
            
            threat_level = "🟢 Low Risk"
            if malicious > 0:
                threat_level = "🔴 High Risk"
            elif suspicious > 0:
                threat_level = "🟡 Medium Risk"
            
            # Generate formatted analysis summary
            summary_lines = [f"## VirusTotal Threat Intelligence Analysis Results\n"]
            summary_lines.append(f"**Target:** `{target}`")
            summary_lines.append(f"**Type:** {ioctype.upper()}")
            summary_lines.append(f"**Threat Level:** {threat_level}\n")
            
            # Detection statistics
            if stats:
                summary_lines.append(f"### Detection Statistics")
                summary_lines.append(f"- **Malicious:** {malicious}/{total}")
                summary_lines.append(f"- **Suspicious:** {suspicious}/{total}")
                summary_lines.append(f"- **Clean:** {stats.get('clean', 0)}/{total}")
                summary_lines.append(f"- **Undetected:** {stats.get('undetected', 0)}/{total}\n")
            
            # Basic information
            if ioctype == 'ip':
                summary_lines.append(f"### IP Information")
                summary_lines.append(f"- **Country:** {analysis_data.get('country', 'Unknown')}")
                summary_lines.append(f"- **ASN:** {analysis_data.get('asn', 'Unknown')}")
                summary_lines.append(f"- **AS Owner:** {analysis_data.get('as_owner', 'Unknown')}")
                summary_lines.append(f"- **Reputation Score:** {analysis_data.get('reputation', 0)}\n")
            
            elif ioctype == 'domain':
                summary_lines.append(f"### Domain Information")
                summary_lines.append(f"- **Registrar:** {analysis_data.get('registrar', 'Unknown')}")
                if analysis_data.get('creation_date'):
                    from datetime import datetime
                    creation_date = datetime.fromtimestamp(analysis_data['creation_date']).strftime('%Y-%m-%d')
                    summary_lines.append(f"- **Creation Date:** {creation_date}")
                summary_lines.append(f"- **Reputation Score:** {analysis_data.get('reputation', 0)}\n")
            
            elif ioctype == 'url':
                summary_lines.append(f"### URL Information")
                summary_lines.append(f"- **Final URL:** {analysis_data.get('final_url', 'Unknown')}")
                summary_lines.append(f"- **Page Title:** {analysis_data.get('title', 'Unknown')}")
                summary_lines.append(f"- **Times Submitted:** {analysis_data.get('times_submitted', 0)}")
                summary_lines.append(f"- **Reputation Score:** {analysis_data.get('reputation', 0)}\n")
            
            # Relationship information statistics
            relations = analysis_data.get('relations', {})
            if relations:
                summary_lines.append(f"### Relationship Information")
                for rel_type, rel_data in relations.items():
                    if rel_data:
                        count = len(rel_data)
                        rel_name = {
                            'communicating_files': 'Communicating Files',
                            'downloaded_files': 'Downloaded Files',
                            'referrer_files': 'Referrer Files',
                            'urls': 'Related URLs',
                            'subdomains': 'Subdomains',
                            'resolutions': 'DNS Resolutions'
                        }.get(rel_type, rel_type)
                        summary_lines.append(f"- **{rel_name}:** {count} items")
                summary_lines.append("")
            
            analysis_summary = "\n".join(summary_lines)
        else:
            result["error"] = "Failed to retrieve VirusTotal analysis data"
            analysis_summary = "VirusTotal analysis failed: No data retrieved"
    
    except Exception as e:
        result["error"] = f"VirusTotal analysis exception: {str(e)}"
        analysis_summary = f"VirusTotal analysis failed: {str(e)}"
    
    #print('\n[virustotal_analysis] Node Return Value:', json.dumps(result, ensure_ascii=False, indent=2))
    return {'virustotal_analysis_result': [analysis_summary], 'virustotal_analysis_raw': result}


def comprehensive_threat_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    """Comprehensive threat intelligence analysis node - integrates all analysis functions"""
    from urllib.parse import urlparse
    configurable = Configuration.from_runnable_config(config)
    # Initialize logger
    logger = get_logger()
    logger.node_enter("comprehensive_threat_analysis")
    
    # Get target and type
    logger.model_config(f"Query Generator Model: {state.get('reasoning_model', configurable.query_generator_model)}")
    # logger.info(f"initial_search_query_count: {state['initial_search_query_count']}")
    # logger.info(f"max_research_loops: {state['max_research_loops']}")
    
    # API search do not support query with proxy.
    os.environ['HTTP_PROXY'] = ''
    os.environ['HTTPS_PROXY'] = ''

    # Initialize comprehensive results
    comprehensive_result = {
        "target": '',
        "type": '',
        "timestamp": int(time.time()),
        "analysis_results": {},
        "threat_assessment": {},
        "summary": ""
    }
    
    # Store all raw data
    all_raw_data = {}
    target = ''
    ioctype = ''
    releated_iocs = []
    releated_analysis_flag = state.get('releated_analysis_flag', False)
    try:
        # 1. IOC analysis
        logger.info("执行IOC分析...")
        
        ioc_result = ioc_analysis(state, releated_analysis_flag)
        if 'end_task' in ioc_result and ioc_result['end_task']:
            return {'end_task': True}
        if 'ioc_analysis_result' in ioc_result:
            comprehensive_result["analysis_results"]["ioc"] = ioc_result['ioc_analysis_result']
            all_raw_data["ioc"] = state.get('ioc_analysis_raw', {})
        
        target = ioc_result.get('ioc', '')
        ioctype = ioc_result.get('ioctype', '')
        releated_iocs = ioc_result.get('releated_iocs', [])
        threat_level = ioc_result.get('threat_level', '')
        state['ioc'] = target
        state['ioctype'] = ioctype
        state['releated_iocs'] = releated_iocs
        comprehensive_result["target"] = target
        comprehensive_result["type"] = ioctype

        logger.info(f"分析目标: {target} (类型: {ioctype})")

        # 2. ZoomEye analysis
        logger.info("执行ZoomEye分析...")
        zoomeye_result = zoomeye_analysis(state, config)
        if 'zoomeye_analysis_result' in zoomeye_result:
            comprehensive_result["analysis_results"]["zoomeye"] = zoomeye_result['zoomeye_analysis_result']
            all_raw_data["zoomeye"] = state.get('zoomeye_analysis_raw', {})
        
        # 3. OTX analysis
        logger.info("执行OTX分析...")
        otx_result = otx_analysis(state, config)
        if 'otx_analysis_result' in otx_result:
            comprehensive_result["analysis_results"]["otx"] = otx_result['otx_analysis_result']
            all_raw_data["otx"] = state.get('otx_analysis_raw', {})
        
        # 4. DNS analysis (for domains and URLs)
        if ioctype in ['domain', 'url']:
            logger.info("执行DNS分析...")
            dns_result = dns_analysis(state, config)
            if 'dns_analysis_result' in dns_result:
                comprehensive_result["analysis_results"]["dns"] = dns_result['dns_analysis_result']
                all_raw_data["dns"] = state.get('dns_analysis_raw', {})
        
        # 5. Get IP address (for IP analysis)
        target_ip = target
        if ioctype == 'domain':
            try:
                import dns.resolver
                answers = dns.resolver.resolve(target, 'A')
                if answers:
                    target_ip = str(answers[0])
                    logger.debug(f"域名 {target} 解析到IP: {target_ip}")
            except Exception as e:
                logger.debug(f"域名解析失败: {e}")
        elif ioctype == 'url':
            try:
                parsed = urlparse(target)
                hostname = parsed.netloc
                if hostname:
                    import dns.resolver
                    answers = dns.resolver.resolve(hostname, 'A')
                    if answers:
                        target_ip = str(answers[0])
                        logger.debug(f"URL {target} 主机名 {hostname} 解析到IP: {target_ip}")
            except Exception as e:
                logger.debug(f"URL主机名解析失败: {e}")
        
        # 6. IP analysis
        if target_ip and target_ip != target:
            # Update IP and type in state
            ip_state = dict(state)
            ip_state['ip'] = target_ip
            ip_state['ioctype'] = 'ip'
            logger.info(f"执行IP分析: {target_ip}")
            ip_result = ip_analysis(ip_state, config)
            if 'ip_analysis_result' in ip_result:
                comprehensive_result["analysis_results"]["ip"] = ip_result['ip_analysis_result']
                all_raw_data["ip"] = ip_result.get('ip_analysis_raw', {})
        elif ioctype == 'ip':
            logger.info("执行IP分析...")
            ip_result = ip_analysis(state, config)
            if 'ip_analysis_result' in ip_result:
                comprehensive_result["analysis_results"]["ip"] = ip_result['ip_analysis_result']
                all_raw_data["ip"] = ip_result.get('ip_analysis_raw', {})
        
        # 7. VirusTotal analysis
        logger.info("执行VirusTotal分析...")
        vt_result = virustotal_analysis(state, config)
        if 'virustotal_analysis_result' in vt_result:
            comprehensive_result["analysis_results"]["virustotal"] = vt_result['virustotal_analysis_result']
            all_raw_data["virustotal"] = vt_result.get('virustotal_analysis_raw', {})
        
        # 8. Generate threat assessment
        threat_scores = []
        threat_indicators = []
        
        # Get threat score from VirusTotal
        vt_raw = all_raw_data.get('virustotal', {})
        if vt_raw.get('status') == 'success':
            vt_data = vt_raw.get('data', {})
            stats = vt_data.get('last_analysis_stats', {})
            malicious = stats.get('malicious', 0)
            suspicious = stats.get('suspicious', 0)
            total = sum(stats.values()) if stats else 0
            
            if total > 0:
                threat_score = ((malicious * 2 + suspicious) / total) * 100
                threat_scores.append(threat_score)
                if malicious > 0:
                    threat_indicators.append(f"VirusTotal detected {malicious} malicious engine reports")
                elif suspicious > 0:
                    threat_indicators.append(f"VirusTotal detected {suspicious} suspicious engine reports")
        
        # Get threat score from AbuseIPDB
        ip_raw = all_raw_data.get('ip', {})
        if ip_raw.get('status') == 'success':
            ip_data = ip_raw.get('data', {})
            abuseipdb = ip_data.get('abuseipdb', {})
            if abuseipdb.get('status') == 'success':
                abuse_confidence = abuseipdb['data'].get('abuse_confidence', 0)
                if abuse_confidence > 0:
                    threat_scores.append(abuse_confidence)
                    threat_indicators.append(f"AbuseIPDB malicious confidence: {abuse_confidence}%")
        
        # Calculate comprehensive threat level
        if not threat_level:
            if threat_scores:
                avg_threat_score = sum(threat_scores) / len(threat_scores)
                if avg_threat_score >= 75:
                    threat_level = "🔴 High Risk"
                    threat_color = "high"
                elif avg_threat_score >= 25:
                    threat_level = "🟡 Medium Risk"
                    threat_color = "medium"
                else:
                    threat_level = "🟢 Low Risk"
                    threat_color = "low"
            else:
                threat_level = "🟢 Low Risk"
                threat_color = "low"
                avg_threat_score = 0
        else:
            if threat_level == '高危':
                threat_level = "🔴 High Risk"
                avg_threat_score = 80
                threat_color = "high"
            elif threat_level == '中危':
                avg_threat_score = 60
                threat_level = "🟡 Medium Risk"
                threat_color = "medium"
            else:
                threat_level = "🟢 Low Risk"
                avg_threat_score = 0
                threat_color = "low"
        
        
        comprehensive_result["threat_assessment"] = {
            "level": threat_level,
            "color": threat_color,
            "score": avg_threat_score,
            "indicators": threat_indicators
        }
        
        # 9. Generate comprehensive analysis summary
        summary_lines = []
        if releated_analysis_flag == False:
            summary_lines.append(f"\n\n# 🔍 Main Threat Intelligence Analysis Report for {target}\n")
            summary_lines.append(f"**Related Intelligence:** `{releated_iocs}`")
        else:
            summary_lines.append(f"\n\n# 🔍 Related Intelligence Analysis Report for {target}\n")
        summary_lines.append(f"**Analysis Target:** `{target}`")
        summary_lines.append(f"**Target Type:** {ioctype.upper()}")
        summary_lines.append(f"**Threat Level:** {threat_level}")
        if avg_threat_score > 0:
            summary_lines.append(f"**Threat Score:** {avg_threat_score:.1f}/100")
        summary_lines.append(f"**Analysis Time:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # Threat indicators
        if threat_indicators:
            summary_lines.append(f"## ⚠️ Threat Indicators")
            for indicator in threat_indicators:
                summary_lines.append(f"- {indicator}")
            summary_lines.append("")
        
        # Analysis results summary for each module
        analysis_modules = [
            ("CandleTI Analysis", "ioc"),
            ("ZoomEye Analysis", "zoomeye"),
            ("OTX Analysis", "otx"),
            ("DNS Analysis", "dns"),
            ("IP Analysis", "ip"),
            ("VirusTotal Analysis", "virustotal")
        ]
        
        summary_lines.append(f"## 📊 Analysis Module Results")
        for module_name, module_key in analysis_modules:
            if module_key in comprehensive_result["analysis_results"]:
                summary_lines.append(f"## {module_name}")
                module_results = comprehensive_result["analysis_results"][module_key]
                if module_results:
                    # Extract key information from each module result
                    result_text = module_results[0] if isinstance(module_results, list) else str(module_results)
                    # Extract first 200 characters as summary
                    if len(result_text) > 200:
                        result_text = result_text[:200] + "..."
                    summary_lines.append(result_text)
                else:
                    summary_lines.append("No data")
                summary_lines.append("")
        
        comprehensive_result["summary"] = "\n".join(summary_lines)
        
        logger.info(f"综合分析完成，威胁等级: {threat_level}")
        
    except Exception as e:
        error_msg = f"Comprehensive analysis error: {str(e)}"
        logger.error(f"综合分析失败: {error_msg}")
        comprehensive_result["error"] = error_msg
        comprehensive_result["summary"] = f"# Comprehensive Threat Intelligence Analysis Failed\n\n**Error Message:** {error_msg}"
    
    # Save all raw data
    comprehensive_result["raw_data"] = all_raw_data
    logger.node_exit("comprehensive_threat_analysis")
    
    if releated_analysis_flag == False:
        return {
            'ioc': target,
            'ioctype': ioctype,
            'releated_iocs': releated_iocs[:5],  # Only query 5 related intelligence items
            'comprehensive_analysis_result': [comprehensive_result["summary"]],
            # 'comprehensive_analysis_raw': comprehensive_result
        }
    else:
        return {
            'comprehensive_analysis_result': [comprehensive_result["summary"]],
            # 'comprehensive_analysis_raw': comprehensive_result
        }


def continue_to_threat_analysis(state: OverallState) -> OverallState:
    """LangGraph node that determines whether to continue with threat analysis.

    Args:
        state (OverallState): Current state of the agent.

    Returns:
        OverallState: Updated state of the agent.
    """
    # Check if threat analysis needs to continue
    if state.get('end_task', False):
        return END

    if state.get('ioctype') == 'url' or len(state.get('releated_iocs', [])) == 0:
        return "generate_query"

    return [
        Send("comprehensive_threat_analysis", {**state, "ioc": ioc, "releated_analysis_flag": True}) for ioc in state.get('releated_iocs')
    ]


def start_threat_analysis(state: OverallState, config: RunnableConfig) -> OverallState:
    """重置状态并开始威胁分析，确保每次都是全新的状态"""
    logger = get_logger()
    logger.info("开始新的威胁分析，重置状态")
    
    # 保留当前消息（前端发送的新消息）
    current_messages = state.get("messages", [])[-1:]
    
    # 创建全新的状态，只保留必要的配置参数
    clean_state = {
        "messages": current_messages,  # 保持当前消息
        "initial_search_query_count": state.get("initial_search_query_count", 1),
        "max_research_loops": state.get("max_research_loops", 1),
        "reasoning_model": state.get("reasoning_model", "gemini-2.5-flash"),
        "research_loop_count": 0,
        # 重置所有累加字段为空列表
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "ioc_analysis_result": [],
        "zoomeye_analysis_result": [],
        "otx_analysis_result": [],
        "dns_analysis_result": [],
        "ip_analysis_result": [],
        "virustotal_analysis_result": [],
        "comprehensive_analysis_result": [],
        # 重置其他字段
        "ioctype": "",
        "ioc": "",
        "ip": "",
        "releated_analysis_flag": False,
        "releated_iocs": [],
        # 重置原始数据字段
        "ioc_analysis_raw": {},
        "zoomeye_analysis_raw": {},
        "otx_analysis_raw": {},
        "dns_analysis_raw": {},
        "ip_analysis_raw": {},
        "virustotal_analysis_raw": {},
        "comprehensive_analysis_raw": {}
    }
    
    # 更新状态为清理后的状态
    state.update(clean_state)
    
    # 调用原始的综合威胁分析
    return comprehensive_threat_analysis(state, config)

# Create the graph for Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Define the nodes we will cycle between
builder.add_node("start_threat_analysis", start_threat_analysis)
builder.add_node("comprehensive_threat_analysis", comprehensive_threat_analysis)
# builder.add_node("ioc_analysis", ioc_analysis)
# builder.add_node("zoomeye_analysis", zoomeye_analysis)
# builder.add_node("otx_analysis", otx_analysis)
# builder.add_node("dns_analysis", dns_analysis)
# builder.add_node("ip_analysis", ip_analysis)
# builder.add_node("virustotal_analysis", virustotal_analysis)
builder.add_node("generate_query", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# Set the entrypoint as `start_threat_analysis`
# This means that this node is the first one called
builder.add_edge(START, "start_threat_analysis") 
builder.add_conditional_edges(
    "start_threat_analysis", continue_to_threat_analysis, ["comprehensive_threat_analysis", "generate_query", END]
)
builder.add_edge("comprehensive_threat_analysis", "generate_query")
# Comprehensive analysis integrates all threat intelligence analysis
# builder.add_edge("comprehensive_threat_analysis", "generate_query")
# Add conditional edge to continue with search queries in a parallel branch
builder.add_conditional_edges(
    "generate_query", continue_to_web_research, ["web_research"]
)
# Reflect on the web research
builder.add_edge("web_research", "reflection")
# Evaluate the research
builder.add_conditional_edges(
    "reflection", evaluate_research, ["web_research", "finalize_answer"]
)
# Finalize the answer
builder.add_edge("finalize_answer", END)

# Add handling for end_task flag
def check_end_task(state):
    """Check if task needs to be ended"""
    if state.get("end_task", False):
        return END
    return "finalize_answer"

# Add conditional edge from ioc_analysis to end
# builder.add_conditional_edges(
#     "ioc_analysis", check_end_task, ["finalize_answer", END]
# )

graph = builder.compile(name="pro-search-agent")


# Separate node for query termination conditions.