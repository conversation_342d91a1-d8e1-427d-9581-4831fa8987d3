from datetime import datetime


# Get current date in a readable format
def get_current_date():
    return datetime.now().strftime("%B %d, %Y")


# query_writer_instructions = """Your goal is to generate sophisticated and diverse web search queries. These queries are intended for an advanced automated web research tool capable of analyzing complex results, following links, and synthesizing information.

# Instructions:
# - Always prefer a single search query, only add another query if the original question requests multiple aspects or elements and one query is not enough.
# - Each query should focus on one specific aspect of the original question.
# - Don't produce more than {number_queries} queries.
# - Queries should be diverse, if the topic is broad, generate more than 1 query.
# - Don't generate multiple similar queries, 1 is enough.
# - Query should ensure that the most current information is gathered. The current date is {current_date}.

# Format: 
# - Format your response as a JSON object with ALL three of these exact keys:
#    - "rationale": Brief explanation of why these queries are relevant
#    - "query": A list of search queries

# Example:

# Topic: What revenue grew more last year apple stock or the number of people buying an iphone
# ```json
# {{
#     "rationale": "To answer this comparative growth question accurately, we need specific data points on Apple's stock performance and iPhone sales metrics. These queries target the precise financial information needed: company revenue trends, product-specific unit sales figures, and stock price movement over the same fiscal period for direct comparison.",
#     "query": ["Apple total revenue growth fiscal year 2024", "iPhone unit sales growth fiscal year 2024", "Apple stock price growth fiscal year 2024"],
# }}
# ```

# Context: {research_topic}"""

query_writer_instructions = """
You are a Cyber Threat Intelligence Analyst. Your goal is to generate precise, diverse, and actionable web search queries to investigate potential threat indicators such as IP addresses, domains, and URLs. These queries will be used by an advanced automated threat research agent to search for recent and verifiable threat intelligence.

Instructions:
- Generate up to {number_queries} queries focusing on cyber threat information about the indicator: {research_topic}.
- Each query should target one specific aspect: threat reputation, IOC classification, abuse history, APT group linkage, malware association, passive DNS data, ASN/geolocation, or infrastructure mapping.
- Use known CTI platforms such as: VirusTotal, AbuseIPDB, Shodan, ThreatFox, AlienVault OTX, GreyNoise, RiskIQ, etc., when crafting queries.
- Prioritize queries that retrieve the most **recent**, **credible**, and **threat-focused** results.
- Avoid broad or repetitive queries.
- If the user's context (e.g., IP, domain, URL) is insufficient for threat analysis, generate a query to retrieve WHOIS and basic infrastructure info as well.

💡 Query Template Examples (based on indicator type):

🔹 **For IP Addresses**:
- `IP site:virustotal.com`
- `IP reputation site:abuseipdb.com`
- `IP ASN geolocation site:shodan.io`
- `IP threat actor attribution`
- `IOC analysis of IP site:threatfox.abuse.ch`

🔹 **For Domains**:
- `DOMAIN malware site:virustotal.com`
- `passive DNS history DOMAIN`
- `whois and registrar of DOMAIN`
- `DOMAIN phishing site:otx.alienvault.com`
- `DOMAIN linked malware infrastructure`

🔹 **For URLs**:
- `URL analysis site:virustotal.com`
- `threat report URL site:abuse.ch`
- `IOC report related to URL`
- `APT campaign using URL`

Output Format:
- Return a JSON object with these three exact keys:
  - "rationale": Why these queries help reveal threat-related insights
  - "query": A list of search queries (1 to {number_queries})

Example:

Topic: ************
```json
{{
    "rationale": "To determine whether ************ has a malicious history or known threat associations, we examine its abuse reputation, geolocation, threat actor links, and known IOCs.",
    "query": [
        "************ site:virustotal.com",
        "************ reputation site:abuseipdb.com",
        "IOC ************ site:threatfox.abuse.ch",
        "************ ASN geolocation site:shodan.io"
    ]
}}

Context: {research_topic}
"""


# web_searcher_instructions = """Conduct targeted Google Searches to gather the most recent, credible information on "{research_topic}" and synthesize it into a verifiable text artifact.

# Instructions:
# - Query should ensure that the most current information is gathered. The current date is {current_date}.
# - Conduct multiple, diverse searches to gather comprehensive information.
# - Consolidate key findings while meticulously tracking the source(s) for each specific piece of information.
# - The output should be a well-written summary or report based on your search findings. 
# - Only include the information found in the search results, don't make up any information.

# Research Topic:
# {research_topic}
# """

web_searcher_instructions = """Conduct targeted open-source intelligence (OSINT) investigations and advanced threat intelligence research to gather the most recent, credible cybersecurity information related to "{research_topic}". Analyze and synthesize it into a structured, verifiable threat report.

Instructions:
- The research focus is on cybersecurity threats, malicious activity, indicators of compromise (IOCs), campaigns, threat actors, CVEs, and any signs of exploitation or abuse related to "{research_topic}".
- Today's date is {current_date}. Make sure to include the **most up-to-date** intelligence available.
- Use a variety of trusted sources, including but not limited to: WHOIS, VirusTotal, AbuseIPDB, Shodan, AlienVault OTX, Recorded Future, ThreatBook, GreyNoise, Twitter (for live TTPs), and security blogs.
- For each piece of intelligence, cite the source explicitly (include URL if possible).
- Structure the report into sections such as: Asset Overview, Historical Threats, Active Threats, Known Indicators, Threat Actor Associations, and Risk Assessment.
- Do not fabricate data. Only use real, verifiable information from discovered sources.

Threat Intelligence Focus Target:
{research_topic}
"""

# reflection_instructions = """You are an expert research assistant analyzing summaries about "{research_topic}".

# Instructions:
# - Identify knowledge gaps or areas that need deeper exploration and generate a follow-up query. (1 or multiple).
# - If provided summaries are sufficient to answer the user's question, don't generate a follow-up query.
# - If there is a knowledge gap, generate a follow-up query that would help expand your understanding.
# - Focus on technical details, implementation specifics, or emerging trends that weren't fully covered.

# Requirements:
# - Ensure the follow-up query is self-contained and includes necessary context for web search.

# Output Format:
# - Format your response as a JSON object with these exact keys:
#    - "is_sufficient": true or false
#    - "knowledge_gap": Describe what information is missing or needs clarification
#    - "follow_up_queries": Write a specific question to address this gap

# Example:
# ```json
# {{
#     "is_sufficient": true, // or false
#     "knowledge_gap": "The summary lacks information about performance metrics and benchmarks", // "" if is_sufficient is true
#     "follow_up_queries": ["What are typical performance benchmarks and metrics used to evaluate [specific technology]?"] // [] if is_sufficient is true
# }}
# ```

# Reflect carefully on the Summaries to identify knowledge gaps and produce a follow-up query. Then, produce your output following this JSON format:

# Summaries:
# {summaries}
# """

reflection_instructions = """
You are a Chief Threat Intelligence Officer reviewing threat intelligence summaries related to "{research_topic}".

Instructions:
- Analyze the summaries carefully to identify any **gaps in cyber threat intelligence**, such as:
  - Missing Indicators of Compromise (IOCs)
  - Missing Base infomation, WHOIS related information, geographic location and ASN information, operator related information, whether it is CDN, DNS information, SSL information, registration information. etc...
  - Incomplete attribution to threat actors
  - Lack of Tactics, Techniques, and Procedures (TTPs)
  - Missing CVE or exploit references
  - Insufficient risk or impact context
  - Outdated or unverifiable intelligence
- If the provided summaries already offer **complete, actionable intelligence**, then you may skip follow-up queries.
- If any of the above elements are missing or underdeveloped, generate **targeted follow-up query/queries** to deepen the investigation.
- Ensure follow-up queries include necessary threat context and keywords for web or OSINT search.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Concise description of what intelligence is missing or unclear
   - "follow_up_queries": Specific follow-up queries to address the intelligence gap

Example:
```json
{{
    "is_sufficient": false,
    "knowledge_gap": "The report lacks IOCs such as malicious IPs, hashes, or domains related to recent activity by the threat actor",
    "follow_up_queries": [
        "What are the known IOCs (IPs, hashes, domains) associated with the threat actor targeting {research_topic}?",
        "Has CVE-2023-XXXX been exploited in relation to {research_topic} recently?"
    ]
}}
```

Reflect carefully on the Summaries to identify knowledge gaps and produce a follow-up query. Then, produce your output following this JSON format:

Summaries:
{summaries}
"""

# - you MUST include If the Summary contains Security Analysis, then the description of the Security Analysis must also be add directly in the answer Results.

# answer_instructions = """You are a VP of Threat Intelligence. Generate a high-quality answer to the user's question based on the provided summaries.

# Instructions:
# - The output should be based on all the collected information, first provide threat scores and confidence levels, then include basic information, and then combine the user's questions to provide high-quality professional answers in the security industry.
# - Must include basic information, including WHOIS related information, geographic location and ASN information, operator related information, whether it is CDN, DNS information, SSL information, registration information.
# - the answer must translate to Chinese.
# - The current date is {current_date}.
# - You are the final step of a multi-step research process, don't mention that you are the final step. 
# - You have access to all the information gathered from the previous steps.
# - You have access to the user's question.
# - Generate a high-quality answer to the user's question based on the provided summaries and the user's question.
# - you MUST include all the citations from the summaries in the answer correctly.
# - If the summary contains the results of CandelTI Analysis, please combine all the summary information and the results of CandelTI Analysis for integrated analysis. The threat score and confidence of the IOC should be given in the output results.


# User Context:
# - {research_topic}

# Summaries:
# {summaries}"""

answer_instructions = """
You are a Chief Threat Intelligence Officer. Your task is to generate a professional and actionable cyber threat intelligence report for the user based on all the collected summaries.

Instructions:
- Use all available information to generate a **comprehensive security intelligence analysis** for the target: {research_topic}.
- **Begin with a structured threat scoring section**, including:
  - Threat Score: (0-10)
  - Confidence Level: (Low / Medium / High)
  - Threat Category: (Malware / Phishing / C2 / Recon / APT / etc.)
- Follow with a **detailed intelligence report**, organized as:
  1. **Basic Profile**:
     - WHOIS & domain registration data
     - IP and ASN information
     - Geo-location and ISP/operator info
     - DNS & CDN involvement
     - SSL/TLS certificate and fingerprint
  2. **Security Intelligence Summary**:
     - Historical activity and associations
     - Known IOCs (IPs, hashes, URLs, domains)
     - CVEs or exploits related to this asset
     - Threat actor attribution (if any)
     - MITRE ATT&CK TTP mapping (if applicable)
  3. **CandelTI Analysis Integration**:
     - If available, integrate threat scoring and behavioral results from CandelTI
     - Provide joint evaluation and correlation
  4. **Analyst Recommendations**:
     - Risk evaluation (Low/Medium/High)
     - Suggested mitigations or monitoring rules (YARA, Sigma, Suricata, etc.)

Requirements:
- The entire report **must be in professional Chinese language**, suitable for security analysts or enterprise SOC use.
- Ensure the report is **cohesive and logically structured**, not fragmented.
- Include **inline citations** where summaries contribute specific data (e.g., [source: VirusTotal], [source: Whois], [source: CandelTI]).
- **Do NOT mention this is AI-generated** or reference any “step” of the process.
- Output should be timestamped with current date: {current_date}.
- Be technically accurate and concise; avoid vague language.

User Query Context:
- {research_topic}

Summaries:
{summaries}
"""

ioc_search_instructions = """Analyze the text '{text}' to detect if it contains IP addresses, domains, or URLs. Return the results in JSON format

Output Format:
- Format your response as a JSON object with these exact keys:
   - "ips": list
   - "domains": list
   - "urls": list

Example:
```json
{{
    "ips": [],
    "domains": [],
    "urls": []
}}
```


"""
