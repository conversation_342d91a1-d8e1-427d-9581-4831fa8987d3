import logging
import sys
from typing import Optional
from enum import Enum
import json
from datetime import datetime

class LogLevel(Enum):
    """日志等级枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class ThreatAnalysisLogger:
    """威胁分析专用日志记录器"""
    
    def __init__(self, name: str = "ThreatAnalysis", level: str = "INFO"):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志等级 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        
        # 自定义格式化器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
    
    def set_level(self, level: str):
        """动态设置日志等级"""
        self.logger.setLevel(getattr(logging, level.upper()))
        for handler in self.logger.handlers:
            handler.setLevel(getattr(logging, level.upper()))
    
    def node_enter(self, node_name: str, state_info: Optional[dict] = None):
        """节点进入日志"""
        msg = f"🚀 ENTERING NODE: {node_name}"
        if state_info:
            msg += f" | State: {json.dumps(state_info, ensure_ascii=False)}"
        self.logger.info(msg)
    
    def node_exit(self, node_name: str, result_info: Optional[dict] = None):
        """节点退出日志"""
        msg = f"✅ EXITING NODE: {node_name}"
        if result_info:
            msg += f" | Result: {json.dumps(result_info, ensure_ascii=False)}"
        self.logger.info(msg)
    
    def debug(self, message: str, **kwargs):
        """调试级别日志"""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息级别日志"""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告级别日志"""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误级别日志"""
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误级别日志"""
        self._log_with_context(logging.CRITICAL, message, **kwargs)
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """带上下文的日志记录"""
        if kwargs:
            context = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            message = f"{message} | {context}"
        self.logger.log(level, message)
    
    def api_request(self, api_name: str, url: str, method: str = "GET"):
        """API请求日志"""
        self.debug(f"🌐 API REQUEST: {api_name}", method=method, url=url)
    
    def api_response(self, api_name: str, status_code: int, response_size: int = 0):
        """API响应日志"""
        if status_code == 200:
            self.debug(f"✅ API SUCCESS: {api_name}", status=status_code, size=f"{response_size}B")
        else:
            self.warning(f"⚠️ API FAILED: {api_name}", status=status_code)
    
    def analysis_start(self, analysis_type: str, target: str):
        """分析开始日志"""
        self.info(f"🔍 ANALYSIS START: {analysis_type}", target=target)
    
    def analysis_complete(self, analysis_type: str, target: str, result_count: int = 0):
        """分析完成日志"""
        self.info(f"✅ ANALYSIS COMPLETE: {analysis_type}", target=target, results=result_count)
    
    def analysis_error(self, analysis_type: str, target: str, error: str):
        """分析错误日志"""
        self.error(f"❌ ANALYSIS ERROR: {analysis_type}", target=target, error=error)
    
    def threat_assessment(self, target: str, threat_level: str, score: float = 0):
        """威胁评估日志"""
        emoji = "🔴" if "High" in threat_level else "🟡" if "Medium" in threat_level else "🟢"
        self.info(f"{emoji} THREAT ASSESSMENT: {threat_level}", target=target, score=f"{score:.1f}")
    
    def ioc_found(self, ioc_type: str, count: int, details: Optional[dict] = None):
        """IOC发现日志"""
        self.info(f"🎯 IOC FOUND: {ioc_type}", count=count, details=details or {})
    
    def model_config(self, model_name: str, **config):
        """模型配置日志"""
        self.info(f"🤖 MODEL CONFIG: {model_name}", **config)
    
    def performance_metric(self, operation: str, duration: float, **metrics):
        """性能指标日志"""
        self.debug(f"⏱️ PERFORMANCE: {operation}", duration=f"{duration:.2f}s", **metrics)

# 全局日志记录器实例
_global_logger: Optional[ThreatAnalysisLogger] = None

def get_logger(name: str = "ThreatAnalysis", level: str = "INFO") -> ThreatAnalysisLogger:
    """获取全局日志记录器实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = ThreatAnalysisLogger(name, level)
    return _global_logger

def set_log_level(level: str):
    """设置全局日志等级"""
    logger = get_logger()
    logger.set_level(level)

# 便捷函数
def debug(message: str, **kwargs):
    """调试日志便捷函数"""
    get_logger().debug(message, **kwargs)

def info(message: str, **kwargs):
    """信息日志便捷函数"""
    get_logger().info(message, **kwargs)

def warning(message: str, **kwargs):
    """警告日志便捷函数"""
    get_logger().warning(message, **kwargs)

def error(message: str, **kwargs):
    """错误日志便捷函数"""
    get_logger().error(message, **kwargs)

def critical(message: str, **kwargs):
    """严重错误日志便捷函数"""
    get_logger().critical(message, **kwargs)

def node_enter(node_name: str, state_info: Optional[dict] = None):
    """节点进入日志便捷函数"""
    get_logger().node_enter(node_name, state_info)

def node_exit(node_name: str, result_info: Optional[dict] = None):
    """节点退出日志便捷函数"""
    get_logger().node_exit(node_name, result_info)