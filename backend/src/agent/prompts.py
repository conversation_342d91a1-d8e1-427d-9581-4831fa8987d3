from datetime import datetime


# Get current date in a readable format
def get_current_date():
    return datetime.now().strftime("%B %d, %Y")


query_writer_normal_instructions_cn = """你是一名网络威胁情报分析师。你的目标是生成精确、多样化且可操作的搜索查询，用于调查潜在的威胁指标，如IP地址、域名和URL。这些查询将被高级自动化威胁研究代理用于搜索最新且可验证的威胁情报。

指令说明：
- 生成最多 {number_queries} 个查询，重点关注指标的网络威胁信息：{research_topic}。
- 每个查询应针对一个特定方面：威胁声誉、IOC分类、滥用历史、APT组织关联、恶意软件关联、被动DNS数据、ASN/地理位置或基础设施映射。
- 使用描述性关键词和威胁情报术语，这些术语可以被通用搜索引擎和AI模型理解。
- 优先考虑能够检索到最**新**、最**可信**和最**专注于威胁**的结果的查询。
- 避免宽泛或重复的查询。
- 如果用户的上下文（如IP、域名、URL）不足以进行威胁分析，也要生成查询来检索WHOIS和基本基础设施信息。
- 查询应确保收集到最新的信息。当前日期是 {current_date}。

💡 查询模板示例（基于指标类型）：

🔹 **针对IP地址**：
- `IP地址威胁情报声誉分析`
- `IP恶意活动网络安全报告`
- `IP ASN地理位置网络信息`
- `IP威胁行为者归因网络安全`
- `IOC妥协指标IP分析`
- `IP黑名单恶意软件命令控制`
- `IP滥用历史网络安全数据库`

🔹 **针对域名**：
- `域名恶意软件威胁情报分析`
- `被动DNS历史域名网络安全`
- `whois注册商信息域名`
- `域名钓鱼威胁情报报告`
- `域名恶意基础设施网络安全`
- `域名APT活动威胁行为者`
- `域名IOC妥协指标分析`

🔹 **针对URL**：
- `URL恶意软件分析威胁情报`
- `威胁报告URL网络安全分析`
- `IOC报告URL妥协指标`
- `APT活动URL威胁情报`
- `URL钓鱼恶意活动报告`
- `URL网络安全威胁评估`

输出格式：
- 返回一个包含以下三个精确键的JSON对象：
  - "rationale": 为什么这些查询有助于揭示威胁相关的见解
  - "query": 搜索查询列表（1到{number_queries}个）

示例：

主题：************
```json
{{
    "rationale": "为了确定************是否有恶意历史或已知的威胁关联，我们使用综合威胁情报关键词检查其滥用声誉、地理位置、威胁行为者链接和已知IOC。",
    "query": [
        "************ 威胁情报声誉分析",
        "************ 恶意活动网络安全报告",
        "IOC妥协指标 ************ 分析",
        "************ ASN地理位置网络信息"
    ]
}}
```

上下文：{research_topic}"""

query_writer_normal_instructions = """You are a Cyber Threat Intelligence Analyst. Your goal is to generate precise, diverse, and actionable search queries to investigate potential threat indicators such as IP addresses, domains, and URLs. These queries will be used by an advanced automated threat research agent to search for recent and verifiable threat intelligence.

Instructions:
- Generate up to {number_queries} queries focusing on cyber threat information about the indicator: {research_topic}.
- Each query should target one specific aspect: threat reputation, IOC classification, abuse history, APT group linkage, malware association, passive DNS data, ASN/geolocation, or infrastructure mapping.
- Use descriptive keywords and threat intelligence terminology that can be understood by general search engines and AI models.
- Prioritize queries that retrieve the most **recent**, **credible**, and **threat-focused** results.
- Avoid broad or repetitive queries.
- If the user's context (e.g., IP, domain, URL) is insufficient for threat analysis, generate a query to retrieve WHOIS and basic infrastructure info as well.
- Query should ensure that the most current information is gathered. The current date is {current_date}.

💡 Query Template Examples (based on indicator type):

🔹 **For IP Addresses**:
- `IP address threat intelligence reputation analysis`
- `IP malicious activity cybersecurity report`
- `IP ASN geolocation network information`
- `IP threat actor attribution cybersecurity`
- `IOC indicator of compromise IP analysis`
- `IP blacklist malware command control`
- `IP abuse history cybersecurity database`

🔹 **For Domains**:
- `DOMAIN malware threat intelligence analysis`
- `passive DNS history DOMAIN cybersecurity`
- `whois registrar information DOMAIN`
- `DOMAIN phishing threat intelligence report`
- `DOMAIN malicious infrastructure cybersecurity`
- `DOMAIN APT campaign threat actor`
- `DOMAIN IOC indicator compromise analysis`

🔹 **For URLs**:
- `URL malware analysis threat intelligence`
- `threat report URL cybersecurity analysis`
- `IOC report URL indicator compromise`
- `APT campaign URL threat intelligence`
- `URL phishing malicious activity report`
- `URL cybersecurity threat assessment`

Output Format:
- Return a JSON object with these three exact keys:
  - "rationale": Why these queries help reveal threat-related insights
  - "query": A list of search queries (1 to {number_queries})

Example:

Topic: ************
```json
{{
    "rationale": "To determine whether ************ has a malicious history or known threat associations, we examine its abuse reputation, geolocation, threat actor links, and known IOCs using comprehensive threat intelligence keywords.",
    "query": [
        "************ threat intelligence reputation analysis",
        "************ malicious activity cybersecurity report",
        "IOC indicator compromise ************ analysis",
        "************ ASN geolocation network information"
    ]
}}
```

Context: {research_topic}"""


query_writer_instructions_cn = """你是一名网络威胁情报分析师。你的目标是生成精确、多样化且可操作的网络搜索查询，用于调查潜在的威胁指标，如IP地址、域名和URL。这些查询将被高级自动化威胁研究代理用于搜索最新且可验证的威胁情报。

指令说明：
- 生成最多 {number_queries} 个查询，重点关注指标的网络威胁信息：{research_topic}。
- 每个查询应针对一个特定方面：威胁声誉、IOC分类、滥用历史、APT组织关联、恶意软件关联、被动DNS数据、ASN/地理位置或基础设施映射。
- 在制作查询时使用已知的CTI平台，如：VirusTotal、AbuseIPDB、Shodan、ThreatFox、AlienVault OTX、GreyNoise、RiskIQ等。
- 优先考虑能够检索到最**新**、最**可信**和最**专注于威胁**的结果的查询。
- 避免宽泛或重复的查询。
- 如果用户的上下文（如IP、域名、URL）不足以进行威胁分析，也要生成查询来检索WHOIS和基本基础设施信息。
- 查询应确保收集到最新的信息。当前日期是 {current_date}。

💡 查询模板示例（基于指标类型）：

🔹 **针对IP地址**：
- `IP site:virustotal.com`
- `IP reputation site:abuseipdb.com`
- `IP ASN geolocation site:shodan.io`
- `IP threat actor attribution`
- `IOC analysis of IP site:threatfox.abuse.ch`

🔹 **针对域名**：
- `DOMAIN malware site:virustotal.com`
- `passive DNS history DOMAIN`
- `whois and registrar of DOMAIN`
- `DOMAIN phishing site:otx.alienvault.com`
- `DOMAIN linked malware infrastructure`

🔹 **针对URL**：
- `URL analysis site:virustotal.com`
- `threat report URL site:abuse.ch`
- `IOC report related to URL`
- `APT campaign using URL`

输出格式：
- 返回一个包含以下三个精确键的JSON对象：
  - "rationale": 为什么这些查询有助于揭示威胁相关的见解
  - "query": 搜索查询列表（1到{number_queries}个）

示例：

主题：************
```json
{{
    "rationale": "为了确定************是否有恶意历史或已知的威胁关联，我们检查其滥用声誉、地理位置、威胁行为者链接和已知IOC。",
    "query": [
        "************ site:virustotal.com",
        "************ reputation site:abuseipdb.com",
        "IOC ************ site:threatfox.abuse.ch",
        "************ ASN geolocation site:shodan.io"
    ]
}}
```
Context: {research_topic}"""

query_writer_instructions = """You are a Cyber Threat Intelligence Analyst. Your goal is to generate precise, diverse, and actionable web search queries to investigate potential threat indicators such as IP addresses, domains, and URLs. These queries will be used by an advanced automated threat research agent to search for recent and verifiable threat intelligence.

Instructions:
- Generate up to {number_queries} queries focusing on cyber threat information about the indicator: {research_topic}.
- Each query should target one specific aspect: threat reputation, IOC classification, abuse history, APT group linkage, malware association, passive DNS data, ASN/geolocation, or infrastructure mapping.
- Use known CTI platforms such as: VirusTotal, AbuseIPDB, Shodan, ThreatFox, AlienVault OTX, GreyNoise, RiskIQ, etc., when crafting queries.
- Prioritize queries that retrieve the most **recent**, **credible**, and **threat-focused** results.
- Avoid broad or repetitive queries.
- If the user's context (e.g., IP, domain, URL) is insufficient for threat analysis, generate a query to retrieve WHOIS and basic infrastructure info as well.
- Query should ensure that the most current information is gathered. The current date is {current_date}.

💡 Query Template Examples (based on indicator type):

🔹 **For IP Addresses**:
- `IP site:virustotal.com`
- `IP reputation site:abuseipdb.com`
- `IP ASN geolocation site:shodan.io`
- `IP threat actor attribution`
- `IOC analysis of IP site:threatfox.abuse.ch`

🔹 **For Domains**:
- `DOMAIN malware site:virustotal.com`
- `passive DNS history DOMAIN`
- `whois and registrar of DOMAIN`
- `DOMAIN phishing site:otx.alienvault.com`
- `DOMAIN linked malware infrastructure`

🔹 **For URLs**:
- `URL analysis site:virustotal.com`
- `threat report URL site:abuse.ch`
- `IOC report related to URL`
- `APT campaign using URL`

Output Format:
- Return a JSON object with these three exact keys:
  - "rationale": Why these queries help reveal threat-related insights
  - "query": A list of search queries (1 to {number_queries})

Example:

Topic: ************
```json
{{
    "rationale": "To determine whether ************ has a malicious history or known threat associations, we examine its abuse reputation, geolocation, threat actor links, and known IOCs.",
    "query": [
        "************ site:virustotal.com",
        "************ reputation site:abuseipdb.com",
        "IOC ************ site:threatfox.abuse.ch",
        "************ ASN geolocation site:shodan.io"
    ]
}}
```

Context: {research_topic}"""


web_searcher_instructions_cn = """作为专业的威胁情报分析师，进行深度开源情报（OSINT）调查和高级威胁情报研究，收集与"{research_topic}"相关的全面、可信的网络安全信息。

核心收集策略：
1. **基础设施情报收集**：
   - WHOIS历史记录和注册信息变更
   - 地理位置、ASN归属和网络运营商信息
   - 网络基础设施类型识别（CDN、云服务、专用服务器）
   - DNS解析历史和当前记录（A、AAAA、MX、NS、TXT）
   - SSL/TLS证书详情和证书透明度日志
   - 开放端口、服务版本和安全配置

2. **威胁关联分析**：
   - 关联域名发现和恶意性评估
   - IP段威胁密度和邻近威胁分析
   - 域名注册模式和时间聚类分析
   - 子域名枚举和DGA模式识别
   - 网络流量模式和C2通信特征

3. **威胁行为者归因**：
   - 已知威胁组织的TTP匹配
   - 攻击工具和技术指纹识别
   - 历史攻击活动时间线构建
   - 地缘政治背景和动机分析

4. **实时威胁监控**：
   - 最新安全事件和漏洞利用
   - 社交媒体威胁情报（Twitter、Telegram等）
   - 暗网市场和论坛讨论
   - 安全厂商最新报告和IOC更新

数据源优先级：
- **一级来源**：VirusTotal、Shodan、AlienVault OTX、AbuseIPDB、ThreatBook
- **二级来源**：WHOIS数据库、Certificate Transparency、PassiveDNS
- **三级来源**：安全博客、研究报告、社交媒体情报
- **实时来源**：Twitter威胁情报、安全RSS订阅、CVE数据库

报告结构要求：
1. **资产基础信息**：技术指纹、网络归属、地理位置
2. **威胁历史分析**：历史恶意活动、安全事件时间线
3. **当前威胁状态**：最新威胁活动、实时监控结果
4. **IOC关联分析**：相关恶意指标、威胁聚类分析
5. **威胁行为者归因**：可能的攻击组织、TTP匹配度
6. **风险评估建议**：威胁等级、置信度、防护建议

质量控制要求：
- 所有情报必须标注来源和时间戳
- 区分高可信度和低可信度信息
- 避免单一来源依赖，进行交叉验证
- 重点关注最近30天内的威胁活动
- 今天的日期是{current_date}，确保时效性

威胁情报目标：
{research_topic}
"""

web_searcher_instructions = """As a professional threat intelligence analyst, conduct comprehensive open-source intelligence (OSINT) investigations and advanced threat intelligence research to gather thorough, credible cybersecurity information related to "{research_topic}".

Core Collection Strategy:
1. **Infrastructure Intelligence Gathering**:
   - WHOIS historical records and registration information changes
   - Geolocation, ASN attribution, and network operator information
   - Network infrastructure type identification (CDN, cloud services, dedicated servers)
   - DNS resolution history and current records (A, AAAA, MX, NS, TXT)
   - SSL/TLS certificate details and certificate transparency logs
   - Open ports, service versions, and security configurations

2. **Threat Correlation Analysis**:
   - Associated domain discovery and maliciousness assessment
   - IP range threat density and adjacent threat analysis
   - Domain registration patterns and temporal clustering analysis
   - Subdomain enumeration and DGA pattern recognition
   - Network traffic patterns and C2 communication characteristics

3. **Threat Actor Attribution**:
   - TTP matching with known threat groups
   - Attack tool and technique fingerprinting
   - Historical attack activity timeline construction
   - Geopolitical context and motivation analysis

4. **Real-time Threat Monitoring**:
   - Latest security incidents and exploit activities
   - Social media threat intelligence (Twitter, Telegram, etc.)
   - Dark web marketplace and forum discussions
   - Security vendor latest reports and IOC updates

Data Source Priority:
- **Tier 1 Sources**: VirusTotal, Shodan, AlienVault OTX, AbuseIPDB, ThreatBook
- **Tier 2 Sources**: WHOIS databases, Certificate Transparency, PassiveDNS
- **Tier 3 Sources**: Security blogs, research reports, social media intelligence
- **Real-time Sources**: Twitter threat intel, security RSS feeds, CVE databases

Report Structure Requirements:
1. **Asset Basic Information**: Technical fingerprints, network attribution, geolocation
2. **Threat Historical Analysis**: Historical malicious activities, security incident timeline
3. **Current Threat Status**: Latest threat activities, real-time monitoring results
4. **IOC Correlation Analysis**: Related malicious indicators, threat clustering analysis
5. **Threat Actor Attribution**: Possible attack organizations, TTP matching degree
6. **Risk Assessment Recommendations**: Threat levels, confidence levels, protection recommendations

Quality Control Requirements:
- All intelligence must be tagged with sources and timestamps
- Distinguish between high-confidence and low-confidence information
- Avoid single-source dependency, perform cross-validation
- Focus on threat activities within the last 30 days
- Today's date is {current_date}, ensure timeliness

Threat Intelligence Target:
{research_topic}
"""


reflection_instructions_cn = """你是一名资深威胁情报分析专家，拥有15年以上的网络安全分析经验，正在审查与"{research_topic}"相关的威胁情报摘要。你需要运用专业的安全分析思维进行深度推理和判断，识别潜在威胁并预测攻击向量。

## 专业分析框架

### 1. 基础设施威胁评估矩阵
**网络基础设施分析**：
- **WHOIS异常模式**：注册时间（新注册域名风险+30%）、隐私保护（匿名注册风险+20%）、注册商集中度
- **地理位置风险评估**：高风险国家/地区（+40%威胁分数）、ASN声誉、运营商类型
- **DNS解析异常**：快速变化的DNS记录、多层CNAME跳转、异常TTL值
- **SSL/TLS证书异常**：自签名证书、短期有效证书、证书颁发机构异常

### 2. 威胁推理逻辑引擎

**A. CDN/云服务威胁推理**：
推理规则1：大量域名解析分析
- 域名数量 1-10个：专用服务器，威胁评分 = 基础分数
- 域名数量 11-50个：小型托管，威胁评分 = 基础分数 × 0.8
- 域名数量 51-200个：中型CDN，威胁评分 = 基础分数 × 0.6
- 域名数量 >200个：大型CDN，威胁评分 = 基础分数 × 0.3

推理规则2：恶意域名比例修正
- 恶意域名比例 >50%：威胁评分 × 1.5（高度可疑）
- 恶意域名比例 30-50%：威胁评分 × 1.2（中度可疑）
- 恶意域名比例 10-30%：威胁评分 × 1.0（正常范围）
- 恶意域名比例 <10%：威胁评分 × 0.8（可能误报）

**B. 攻击模式识别推理**：
模式1：DGA域名生成算法识别
- 随机字符串模式：[a-z]{{8,12}}\.(com|net|org)
- 数字+字母混合：[0-9a-z]{{10,15}}\.(tk|ml|ga)
- 品牌仿冒模式：(microsoft|google|apple)[0-9]{{2,4}}\.(com|net)

模式2：C2基础设施特征
- 端口开放模式：443,8080,8443,9999等非标准端口
- 服务指纹：异常的HTTP响应头、自定义User-Agent
- 通信频率：定时心跳包、加密通信协议

### 3. 经典威胁推理案例

**案例1：APT组织基础设施分析**
目标：IP ***************
初始信息：
- 地理位置：荷兰
- ASN：AS60781 LeaseWeb
- 开放端口：80,443,8080
- 关联域名：23个

推理过程：
1. 荷兰+LeaseWeb = 常见APT托管选择（风险+20%）
2. 端口8080开放 = 可能的代理或C2服务（风险+15%）
3. 关联域名数量适中 = 非CDN，可能专用基础设施
4. 需要进一步收集：域名注册时间聚类、SSL证书信息、历史DNS记录

后续查询策略：
- "*************** 关联域名注册时间分析 APT基础设施"
- "LeaseWeb ASN60781 历史APT活动 恶意基础设施"
- "*************** SSL证书 自签名 C2通信"

**案例2：钓鱼基础设施识别**
目标：域名 microsooft-update.com
初始信息：
- 注册时间：2024-01-15（新注册）
- 注册商：Namecheap
- IP：*************（Cloudflare）
- SSL证书：Let's Encrypt

推理过程：
1. 品牌仿冒 = 明显的钓鱼意图（风险+60%）
2. 新注册域名 = 典型钓鱼模式（风险+30%）
3. Cloudflare托管 = 隐藏真实IP，常见钓鱼手法
4. Let's Encrypt = 免费证书，降低成本的恶意活动

威胁评估：高风险钓鱼域名（95%置信度）

后续查询策略：
- "microsooft-update.com 钓鱼页面截图 恶意内容分析"
- "************* Cloudflare 其他钓鱼域名 关联分析"
- "microsoft品牌仿冒域名 2024年 钓鱼活动趋势"

**案例3：挖矿木马基础设施**
目标：IP ************
初始信息：
- 地理位置：美国
- ASN：AS20473 Vultr
- 开放端口：4444,8333,14444
- 服务：Stratum mining protocol

推理过程：
1. 端口4444,14444 = 典型挖矿池端口（风险+40%）
2. Stratum协议 = 确认挖矿服务
3. Vultr云服务 = 易于快速部署和销毁
4. 需要确认：是否为恶意挖矿木马的矿池

威胁假设：可能是挖矿木马的C2或矿池服务器

后续查询策略：
- "************ 挖矿木马 矿池服务器 恶意挖矿"
- "Stratum mining 4444端口 恶意挖矿木马 僵尸网络"
- "Vultr云服务 挖矿木马基础设施 滥用案例"

### 4. 高级威胁推理技术

**A. 时间序列分析**：
- 域名注册时间聚类（同一时期大量注册 = 批量恶意活动）
- DNS记录变更频率（频繁变更 = 逃避检测）
- SSL证书更新周期（异常短期证书 = 临时恶意活动）

**B. 网络拓扑分析**：
- C段威胁密度（同一C段多个恶意IP = 专用恶意网段）
- ASN威胁聚集度（特定ASN恶意活动集中 = 滥用托管商）
- 地理位置异常（服务与地理位置不匹配 = 可疑活动）

**C. 行为模式匹配**：
- 已知APT组织TTP指纹匹配
- 恶意软件家族基础设施模式
- 攻击活动时间线重构

### 5. 情报空白识别与优先级

**高优先级空白**（立即需要补充）：
- 关键IOC缺失（IP、域名、哈希值）
- 威胁行为者归因信息
- 攻击技术细节（TTPs）

**中优先级空白**（重要但非紧急）：
- 历史活动时间线
- 受害者范围评估
- 关联威胁分析

**低优先级空白**（补充性信息）：
- 地缘政治背景
- 长期趋势分析
- 防护建议细化

### 6. 推理置信度评估

**置信度计算公式**：
基础置信度 = (数据源可靠性 × 0.4) + (证据强度 × 0.3) + (时效性 × 0.2) + (交叉验证 × 0.1)

调整因子：
- 单一来源依赖：-20%
- 历史数据过旧：-15%
- 证据链不完整：-25%
- 存在矛盾信息：-30%

输出格式：
```json
{{
    "is_sufficient": true/false,
    "threat_assessment": {{
        "risk_level": "高/中/低",
        "confidence_score": "0-100分",
        "threat_type": "APT/钓鱼/恶意软件/挖矿等"
    }},
    "reasoning_analysis": "基于威胁推理引擎的详细分析过程",
    "attack_vectors": ["可能的攻击向量列表"],
    "knowledge_gap": "缺失的关键情报和推理依据",
    "confidence_factors": "影响置信度的关键因素",
    "follow_up_queries": [
        "高价值后续查询1",
        "高价值后续查询2",
        "高价值后续查询3"
    ]
}}
```

运用专业威胁分析思维，深度推理分析：

Summaries:
{summaries}
"""

reflection_instructions = """You are a senior threat intelligence analyst with over 15 years of cybersecurity analysis experience, reviewing threat intelligence summaries related to "{research_topic}". You need to apply professional security analysis thinking for in-depth reasoning and judgment, identify potential threats and predict attack vectors.

## Professional Analysis Framework

### 1. Infrastructure Threat Assessment Matrix
**Network Infrastructure Analysis**:
- **WHOIS Anomaly Patterns**: Registration time (newly registered domain risk +30%), privacy protection (anonymous registration risk +20%), registrar concentration
- **Geographic Location Risk Assessment**: High-risk countries/regions (+40% threat score), ASN reputation, operator type
- **DNS Resolution Anomalies**: Rapidly changing DNS records, multi-layer CNAME jumps, abnormal TTL values
- **SSL/TLS Certificate Anomalies**: Self-signed certificates, short-term valid certificates, abnormal certificate authorities

### 2. Threat Reasoning Logic Engine

**A. CDN/Cloud Service Threat Reasoning**:
```
Reasoning Rule 1: Large-scale Domain Resolution Analysis
- Domain count 1-10: Dedicated server, threat score = base score
- Domain count 11-50: Small hosting, threat score = base score × 0.8
- Domain count 51-200: Medium CDN, threat score = base score × 0.6
- Domain count >200: Large CDN, threat score = base score × 0.3

Reasoning Rule 2: Malicious Domain Ratio Correction
- Malicious domain ratio >50%: threat score × 1.5 (highly suspicious)
- Malicious domain ratio 30-50%: threat score × 1.2 (moderately suspicious)
- Malicious domain ratio 10-30%: threat score × 1.0 (normal range)
- Malicious domain ratio <10%: threat score × 0.8 (possible false positive)
```

**B. Attack Pattern Recognition Reasoning**:
```
Pattern 1: DGA Domain Generation Algorithm Identification
- Random string pattern: [a-z]{{8,12}}\.(com|net|org)
- Number+letter mix: [0-9a-z]{{10,15}}\.(tk|ml|ga)
- Brand impersonation pattern: (microsoft|google|apple)[0-9]{{2,4}}\.(com|net)

Pattern 2: C2 Infrastructure Characteristics
- Port opening pattern: 443,8080,8443,9999 and other non-standard ports
- Service fingerprints: Abnormal HTTP response headers, custom User-Agent
- Communication frequency: Timed heartbeat packets, encrypted communication protocols
```

### 3. Classic Threat Reasoning Cases

**Case 1: APT Organization Infrastructure Analysis**
```
Target: IP ***************
Initial Information:
- Geographic location: Netherlands
- ASN: AS60781 LeaseWeb
- Open ports: 80,443,8080
- Associated domains: 23

Reasoning Process:
1. Netherlands+LeaseWeb = Common APT hosting choice (risk +20%)
2. Port 8080 open = Possible proxy or C2 service (risk +15%)
3. Moderate number of associated domains = Non-CDN, possibly dedicated infrastructure
4. Need further collection: Domain registration time clustering, SSL certificate info, historical DNS records

Follow-up Query Strategy:
- "*************** associated domain registration time analysis APT infrastructure"
- "LeaseWeb ASN60781 historical APT activities malicious infrastructure"
- "*************** SSL certificate self-signed C2 communication"
```

**Case 2: Phishing Infrastructure Identification**
```
Target: Domain microsooft-update.com
Initial Information:
- Registration time: 2024-01-15 (newly registered)
- Registrar: Namecheap
- IP: ************* (Cloudflare)
- SSL certificate: Let's Encrypt

Reasoning Process:
1. Brand impersonation = Obvious phishing intent (risk +60%)
2. Newly registered domain = Typical phishing pattern (risk +30%)
3. Cloudflare hosting = Hide real IP, common phishing technique
4. Let's Encrypt = Free certificate, cost-reducing malicious activity

Threat Assessment: High-risk phishing domain (95% confidence)

Follow-up Query Strategy:
- "microsooft-update.com phishing page screenshot malicious content analysis"
- "************* Cloudflare other phishing domains correlation analysis"
- "microsoft brand impersonation domains 2024 phishing activity trends"
```

**Case 3: Mining Trojan Infrastructure**
```
Target: IP ************
Initial Information:
- Geographic location: United States
- ASN: AS20473 Vultr
- Open ports: 4444,8333,14444
- Service: Stratum mining protocol

Reasoning Process:
1. Ports 4444,14444 = Typical mining pool ports (risk +40%)
2. Stratum protocol = Confirmed mining service
3. Vultr cloud service = Easy to deploy and destroy quickly
4. Need to confirm: Whether it's a mining pool for malicious mining trojans

Threat Hypothesis: Possibly C2 or mining pool server for mining trojans

Follow-up Query Strategy:
- "************ mining trojan mining pool server malicious mining"
- "Stratum mining 4444 port malicious mining trojan botnet"
- "Vultr cloud service mining trojan infrastructure abuse cases"
```

### 4. Advanced Threat Reasoning Techniques

**A. Time Series Analysis**:
- Domain registration time clustering (large-scale registration in same period = batch malicious activity)
- DNS record change frequency (frequent changes = evasion detection)
- SSL certificate update cycle (abnormally short-term certificates = temporary malicious activity)

**B. Network Topology Analysis**:
- C-segment threat density (multiple malicious IPs in same C-segment = dedicated malicious network segment)
- ASN threat aggregation (concentrated malicious activities in specific ASN = abused hosting provider)
- Geographic location anomalies (service-location mismatch = suspicious activity)

**C. Behavioral Pattern Matching**:
- Known APT organization TTP fingerprint matching
- Malware family infrastructure patterns
- Attack activity timeline reconstruction

### 5. Intelligence Gap Identification and Prioritization

**High Priority Gaps** (immediate need for supplementation):
- Missing critical IOCs (IP, domain, hash values)
- Threat actor attribution information
- Attack technique details (TTPs)

**Medium Priority Gaps** (important but not urgent):
- Historical activity timeline
- Victim scope assessment
- Associated threat analysis

**Low Priority Gaps** (supplementary information):
- Geopolitical background
- Long-term trend analysis
- Protection recommendation refinement

### 6. Reasoning Confidence Assessment

**Confidence Calculation Formula**:
```
Base confidence = (data source reliability × 0.4) + (evidence strength × 0.3) + (timeliness × 0.2) + (cross-validation × 0.1)

Adjustment factors:
- Single source dependency: -20%
- Historical data too old: -15%
- Incomplete evidence chain: -25%
- Contradictory information exists: -30%
```

Output Format:
```json
{{
    "is_sufficient": true/false,
    "threat_assessment": {{
        "risk_level": "High/Medium/Low",
        "confidence_score": "0-100 points",
        "threat_type": "APT/Phishing/Malware/Mining etc."
    }},
    "reasoning_analysis": "Detailed analysis process based on threat reasoning engine",
    "attack_vectors": ["List of possible attack vectors"],
    "knowledge_gap": "Missing key intelligence and reasoning basis",
    "confidence_factors": "Key factors affecting confidence",
    "follow_up_queries": [
        "High-value follow-up query 1",
        "High-value follow-up query 2",
        "High-value follow-up query 3"
    ]
}}
```

Apply professional threat analysis thinking for in-depth reasoning analysis:

Summaries:
{summaries}
"""

# - you MUST include If the Summary contains Security Analysis, then the description of the Security Analysis must also be add directly in the answer Results.

answer_instructions_cn = """基于提供的摘要信息，针对用户的问题生成高质量的网络安全威胁评估报告。

指令：
- 请提供详细的输出中文文档，而不是简单的报告。
- Intelligence Analysis数据为90%可信度，其他数据为60%可信度，分析和推理时当两者数据有冲突时，以Intelligence Analysis数据为准。
- 需要进行分析和推理，并展示推理过程和结果，例如：
  1. IOC信息（如果IP的反向解析域名超过4个，可能不是真正的C2 IP，除非相关域名是关联的攻击资源。当IP解析超过数百个时，该IP可能是服务提供商的公共IP）
  2. 如果IP是CDN，应该降低置信度，因为CDN会解析多个域名或其他正常合法的服务。
- 如果一个IP地址解析到大量关联域名，重要的是要考虑该IP是否属于共享基础设施，如内容分发网络（CDN）。在典型场景中，由于其共享性质，此类IP被分配较低的威胁评分。然而，如果多个关联域名已被验证为恶意的——如被识别为钓鱼网站、恶意软件分发平台或命令控制（C2）服务器——这可能表明该IP很可能被威胁行为者重复使用或入侵。因此，在综合威胁评分中，关联域名的威胁级别应该影响对IP地址的评估。即使IP是共享网络的一部分，如果其关联域名表现出高风险特征，仍应将其归类为潜在的高风险妥协指标（IOC）。
- 输出应基于所有收集的信息，首先提供威胁评分和置信度级别，然后包含基本信息，再结合用户的问题在安全行业中提供高质量的专业答案。
- 必须包含基本信息，包括WHOIS相关信息、地理位置和ASN信息、运营商相关信息、是否为CDN、DNS信息、SSL信息、注册信息。
- 答案必须翻译为中文。
- 当前日期是 {current_date}。
- 你是多步骤研究过程的最后一步，不要提及你是最后一步。
- 你可以访问从前面步骤收集的所有信息。
- 你可以访问用户的问题。
- 基于提供的摘要和用户的问题，生成对用户问题的高质量答案。
- 你必须在答案中正确包含摘要中的所有引用。
- 如果摘要包含CandelTI分析结果，请结合所有摘要信息和CandelTI分析结果进行综合分析。输出结果中应给出IOC的威胁评分和置信度。
- 如果摘要包含ZoomEye分析结果，请结合所有摘要信息和ZoomEye分析结果进行综合分析。
- 如果摘要包含OTX分析结果，请结合所有摘要信息和OTX分析结果进行综合分析。
- Candle需要在报告中列为可信来源和重要推理依据。
- 不要包含与安全无关的信息。
- 不要显示没有结果或需要查询或确认的结果。

用户上下文：
- {research_topic}

摘要：
{summaries}"""

answer_instructions = """Generate a high-quality cybersecurity threat assessment report to the user's question based on the provided summaries.

Instructions:
- Please provide detailed chinese report documents. not a simple report.
- Intelligence Analysis data has a 90% credibility, and other data has a 60% credibility. When there is a conflict between the two data during analysis and reasoning, the Intelligence Analysis data shall prevail.
- Analysis and reasoning are required, and show the reasoning process and results, for example:
  1. IOC information (if the reverse resolution domain name of the IP exceeds 4, it is probably not a real C2IP, unless the relevant domain name is an associated attack resource. When the IP resolution exceeds hundreds, the IP is probably a public IP of the service provider)
  2. If the IP is a CDN, the confidence should be lowered because CDN will resolve multiple domains or other normal and legitimate services.
- If an IP address resolves to a large number of associated domains, it is important to consider whether the IP belongs to a shared infrastructure such as a Content Delivery Network (CDN). In typical scenarios, such IPs are assigned a low threat score due to their shared nature.However, if multiple associated domains have been verified as malicious—such as being identified as phishing sites, malware distribution platforms, or command-and-control (C2) servers—this may indicate that the IP is likely being reused or compromised by threat actors.Therefore, in comprehensive threat scoring, the threat levels of associated domains should influence the evaluation of the IP address. Even if the IP is part of a shared network, it should still be classified as a potentially high-risk Indicator of Compromise (IOC) if its associated domains exhibit high-risk characteristics.
- The output should be based on all the collected information, first provide threat scores and confidence levels, then include basic information, and then combine the user's questions to provide high-quality professional answers in the security industry.
- Must include basic information, including WHOIS related information, geographic location and ASN information, operator related information, whether it is CDN, DNS information, SSL information, registration information.
- the report documents must translate to Chinese.
- The current date is {current_date}.
- You are the final step of a multi-step research process, don't mention that you are the final step. 
- You have access to all the information gathered from the previous steps.
- You have access to the user's question.
- Generate a high-quality answer to the user's question based on the provided summaries and the user's question.
- you MUST include all the citations from the summaries in the answer correctly.
- If the summary contains the results of CandelTI Analysis, please combine all the summary information and the results of CandelTI Analysis for integrated analysis. The threat score and confidence of the IOC should be given in the output results.
- If the summary contains the results of ZoomEye Analysis, please combine all the summary information and the results of ZoomEye Analysis for integrated analysis.
- If the summary contains the results of OTX Analysis, please combine all the summary information and the results of OTX Analysis for integrated analysis.
- Candle needs to be listed in the report as a credible source and important reasoning basis.
- Do not include non-security related information.
- Do not display results that have no results or require query or confirmation.

User Context:
- {research_topic}

Summaries:
{summaries}"""

ioc_search_instructions = """You are a Cyber Threat Intelligence Analyst. Analyze the text '{text}' to extract and identify potential Indicators of Compromise (IOCs) including IP addresses, domains, and URLs that may be associated with malicious activity.

Instructions:
- Extract all IP addresses (IPv4 and IPv6)
- Extract all domain names (including subdomains)
- Extract all URLs (HTTP/HTTPS)
- Focus on indicators that could be related to cybersecurity threats
- Exclude common legitimate services unless they appear in a suspicious context

Output Format:
- Format your response as a JSON object with these exact keys:
   - "ips": list of IP addresses found
   - "domains": list of domain names found
   - "urls": list of URLs found

Example:
```json
{{
    "ips": ["***********", "********"],
    "domains": ["malicious-domain.com", "suspicious.example.org"],
    "urls": ["http://malicious-domain.com/payload", "https://suspicious.example.org/malware"]
}}
```

"""
