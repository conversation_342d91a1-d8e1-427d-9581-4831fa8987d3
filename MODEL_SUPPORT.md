# 模型支持说明

本项目现在支持两种模型提供商：DeepSeek 和 Google Gemini。

## 支持的模型

### DeepSeek 模型
- `deepseek-ai/DeepSeek-V3` - DeepSeek V3 模型
- `deepseek-ai/DeepSeek-R1` - DeepSeek R1 模型（新增）

### Google Gemini 模型
- `gemini-1.5-pro` - Gemini 1.5 Pro 模型（新增）
- `gemini-1.5-flash` - Gemini 1.5 Flash 模型（新增）

## 环境变量配置

在 `backend/.env` 文件中配置以下环境变量：

```bash
# DeepSeek API 配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Google Gemini API 配置
GEMINI_API_KEY=your_gemini_api_key_here

# 模型配置（可选，有默认值）
QUERY_GENERATOR_MODEL=deepseek-ai/DeepSeek-V3
REFLECTION_MODEL=deepseek-ai/DeepSeek-V3
ANSWER_MODEL=deepseek-ai/DeepSeek-V3
REASONING_MODEL=deepseek-ai/DeepSeek-V3
MODEL_PROVIDER=deepseek

# 研究配置（可选）
NUMBER_OF_INITIAL_QUERIES=3
MAX_RESEARCH_LOOPS=3
```

## 前端使用

1. 在前端界面中，您可以在模型选择下拉菜单中选择不同的模型
2. 系统会自动根据选择的模型确定使用的提供商（DeepSeek 或 Gemini）
3. 选择模型后立即生效，无需重启服务

## API 密钥获取

### DeepSeek API 密钥
1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账号并获取 API 密钥
3. 将密钥配置到 `DEEPSEEK_API_KEY` 环境变量

### Google Gemini API 密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 创建项目并获取 API 密钥
3. 将密钥配置到 `GEMINI_API_KEY` 环境变量

## 技术实现

- 后端使用 `get_llm_model()` 辅助函数根据 `model_provider` 参数动态选择模型实现
- DeepSeek 模型使用 `ChatOpenAI` 类通过 SiliconFlow API 调用
- Gemini 模型使用 `ChatGoogleGenerativeAI` 类直接调用 Google API
- 前端通过 `model_provider` 参数传递模型提供商信息到后端

## 测试集成

### 测试 Gemini 模型

我们提供了一个测试脚本来验证 Gemini 模型集成：

```bash
# 进入后端目录
cd backend

# 运行 Gemini 测试脚本
python test_gemini_integration.py
```

测试脚本会验证：
- API 密钥配置
- Gemini 1.5 Pro 模型连接
- Gemini 1.5 Flash 模型连接
- 模型选择逻辑

### 测试 DeepSeek 模型

```bash
# 运行 DeepSeek 测试脚本
python test_deepseek_integration.py
```

## 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查 `.env` 文件中的 API 密钥是否正确
   - 确保 API 密钥有效且未过期

2. **网络连接问题**
   - 检查防火墙设置
   - 确保能够访问相应的 API 端点

3. **模型不可用**
   - 检查模型名称是否正确
   - 确认模型在您的地区可用

4. **配额超限**
   - 检查 API 使用配额
   - 考虑升级 API 计划

### 调试模式

在开发环境中，您可以启用详细日志来调试问题：

```bash
# 设置日志级别
export LOG_LEVEL=DEBUG

# 启动后端服务
langgraph dev
```

## 注意事项

1. **API 密钥安全**: 请妥善保管您的 API 密钥，不要将其提交到版本控制系统中
2. **网络连接**: 确保服务器能够访问相应的 API 端点
3. **配额限制**: 注意各个模型提供商的使用配额和费率限制
4. **模型可用性**: 某些模型可能在特定地区不可用，请查看提供商的文档
5. **错误处理**: 系统会自动处理 API 调用失败的情况，并提供相应的错误信息
6. **性能考虑**: 不同模型的响应速度和成本不同，请根据需求选择合适的模型